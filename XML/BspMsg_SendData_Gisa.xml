<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
	<s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
		<SendDataInputObject xmlns="http://reference.e-government.gv.at/namespace/elaktrans/3#">
			<CommonInputParameter>
				<SourceSystemId>at.gv.wien.gisa</SourceSystemId>
				<DestinationSystemId />
				<VKZType>L9MBA02</VKZType>
				<Procedure>WT-GE</Procedure>
				<RequestIdentifier>
					<ConversationIdentifier>160323004</ConversationIdentifier>
					<SequenceIdentifier>1</SequenceIdentifier>
				</RequestIdentifier>
			</CommonInputParameter>
			<Purpose>OUTPUT</Purpose>
			<LayerControl>
				<Layer3SendControl>
					<ContainsModifiedBasicData>true</ContainsModifiedBasicData>
					<ContainsModifiedCatchwords>false</ContainsModifiedCatchwords>
					<ContainsModifiedLocations>false</ContainsModifiedLocations>
					<ContainsModifiedParticipants>false</ContainsModifiedParticipants>
					<ContainsModifiedState>false</ContainsModifiedState>
				</Layer3SendControl>
				<Layer2SendControl>
					<ContainsModifiedBasicData>true</ContainsModifiedBasicData>
					<ContainsModifiedCatchwords>false</ContainsModifiedCatchwords>
					<ContainsModifiedLocations>false</ContainsModifiedLocations>
					<ContainsModifiedParticipants>false</ContainsModifiedParticipants>
					<ContainsModifiedState>false</ContainsModifiedState>
				</Layer2SendControl>
				<Layer1SendControl>
					<ContainsModifiedBasicData>true</ContainsModifiedBasicData>
					<ContainsModifiedCatchwords>false</ContainsModifiedCatchwords>
					<ContainsModifiedLocations>true</ContainsModifiedLocations>
					<ContainsModifiedParticipants>true</ContainsModifiedParticipants>
					<ContainsModifiedState>false</ContainsModifiedState>
				</Layer1SendControl>
				<Layer0SendControl>
					<ContainsModifiedBasicData>true</ContainsModifiedBasicData>
					<ContainsModifiedBinaryContent>true</ContainsModifiedBinaryContent>
					<ContainsModifiedCatchwords>false</ContainsModifiedCatchwords>
					<ContainsModifiedLocations>false</ContainsModifiedLocations>
					<ContainsModifiedParticipants>false</ContainsModifiedParticipants>
					<ContainsModifiedState>false</ContainsModifiedState>
				</Layer0SendControl>
				<Layer3ResponseControl>
					<IncludeViewUrl>true</IncludeViewUrl>
				</Layer3ResponseControl>
				<Layer2ResponseControl>
					<IncludeViewUrl>true</IncludeViewUrl>
				</Layer2ResponseControl>
				<Layer1ResponseControl>
					<IncludeViewUrl>true</IncludeViewUrl>
				</Layer1ResponseControl>
				<Layer0ResponseControl>
					<IncludeViewUrl>true</IncludeViewUrl>
				</Layer0ResponseControl>
				<Layer3>
					<Identifier>
						<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">192-2014</Identification>
					</Identifier>
					<ReferencedIdentifier>
						<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">160323001</Identification>
					</ReferencedIdentifier>
					<Layer2>
						<Identifier>
							<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">192-2014-1</Identification>
						</Identifier>
						<ReferencedIdentifier>
							<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">160323002</Identification>
						</ReferencedIdentifier>
						<Layer1>
							<ReferencedIdentifier>
								<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">160323004</Identification>
							</ReferencedIdentifier>
							<Layer0>
								<ReferencedIdentifier>
									<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">160323005</Identification>
								</ReferencedIdentifier>
							</Layer0>
						</Layer1>
					</Layer2>
				</Layer3>
			</LayerControl>
			<Ediakt>
				<Header xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#" />
				<MetaData xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">
					<Identifier>
						<Identification />
					</Identifier>
					<ReferencedIdentifier>
						<Identification />
					</ReferencedIdentifier>
				</MetaData>
				<Payload xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">
					<Layer3>
						<Subject>Eduard Mayer2, geb. am 10.10.70
Gemüsehandel
Waldgasse 77/30, 1073 Wien, Österreich</Subject>
						<MetaData>
							<Identifier>
								<Identification>192-2014</Identification>
							</Identifier>
							<ReferencedIdentifier>
								<Identification>160323001</Identification>
							</ReferencedIdentifier>
						</MetaData>
						<Payload>
							<Layer2>
								<Subject>GesFall A</Subject>
								<MetaData>
									<Identifier>
										<Identification>192-2014-1</Identification>
									</Identifier>
									<ReferencedIdentifier>
										<Identification>160323002</Identification>
									</ReferencedIdentifier>
								</MetaData>
								<Payload>
									<Layer1>
										<Subject>vom Referenten beim Transfer eingegebener Betreff</Subject>
										<MetaData>
											<Identifier>
												<Identification />
											</Identifier>
											<ReferencedIdentifier>
												<Identification>160323004</Identification>
											</ReferencedIdentifier>
											<Participants>
												<Participant>
													<PhysicalPerson xmlns="http://reference.e-government.gv.at/namespace/persondata/20020228#">
														<Identification>
															<Value>127</Value>
															<Type>ID</Type>
														</Identification>
														<Identification>
															<Value>gbSq6CYanonymisiertzKBHO+Mw=</Value>
															<Type>bPK:urn:publicid:gv.at:ecdid+BMI+WT</Type>
														</Identification>
														<Identification>
															<Value>1234567</Value>
															<Type>Sozialversicherungsnummer</Type>
														</Identification>
														<Identification>
															<Value>3</Value>
															<Type>BlockNr</Type>
														</Identification>
														<Identification>
															<Value>3</Value>
															<Type>HuellenobjektNr</Type>
														</Identification>
														<Identification>
															<Value>freigegeben</Value>
															<Type>Status</Type>
														</Identification>
														<Identification>
															<Value>Inhaber</Value>
															<Type>Art der Beteiligung</Type>
														</Identification>
														<Identification>
															<Value>Inhaber 3</Value>
															<Type>Bemerkung</Type>
														</Identification>
														<Name>
															<GivenName>Eduard</GivenName>
															<FamilyName>Mayer79</FamilyName>
															<Affix position="prefix">Dr.</Affix>
															<Affix position="suffix">MSc</Affix>
														</Name>
														<AlternativeName Type="MaidenName">
															<GivenName />
															<FamilyName>Huber79</FamilyName>
														</AlternativeName>
														<Sex>male</Sex>
														<DateOfBirth>1970-10-10</DateOfBirth>
														<PlaceOfBirth>Wien</PlaceOfBirth>
														<CountryOfBirth>Österreich</CountryOfBirth>
														<DateOfDeath>2014-08-21</DateOfDeath>
														<Nationality>
															<ISOCode3>AUT</ISOCode3>
															<CountryNameDE>Österreich</CountryNameDE>
															<GISA_Data xmlns="http://reference.e-government.gv.at/namespace/persondata/specialdata/gisa/20140307#" />
														</Nationality>
													</PhysicalPerson>
													<TypedPostalAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/20020228#">
														<Identification>
															<Value>Wien</Value>
															<Type>Gemeinde</Type>
														</Identification>
														<Identification>
															<Value>90001</Value>
															<Type>Katastralgemeinde</Type>
														</Identification>
														<Identification>
															<Value>536/333</Value>
															<Type>GrundstueckNr</Type>
														</Identification>
														<Type />
														<PostalAddress>
															<CountryName>Österreich</CountryName>
															<PostalCode>1078</PostalCode>
															<Hamlet>Wien</Hamlet>
															<DeliveryAddress>
																<StreetName>Waldgasse</StreetName>
																<BuildingNumber>77/80</BuildingNumber>
																<AddressRegisterEntry>
																	<AddressCode>222</AddressCode>
																	<SubCode>333</SubCode>
																	<ObjectNumber>444</ObjectNumber>
																</AddressRegisterEntry>
															</DeliveryAddress>
														</PostalAddress>
													</TypedPostalAddress>
													<TelephoneAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/20020228#">
														<Number>
															<FormattedNumber>012343456363</FormattedNumber>
														</Number>
													</TelephoneAddress>
													<TelephoneAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/20020228#">
														<Type>Fax</Type>
														<Number>
															<FormattedNumber>012343458888</FormattedNumber>
														</Number>
													</TelephoneAddress>
													<InternetAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/20020228#">
														<Address>a@b.c</Address>
													</InternetAddress>
												</Participant>
												<TypeOfParticipation>EMPFAENGER</TypeOfParticipation>
												<DispatchType>BRIEF_RSB</DispatchType>
											</Participants>
											<Participants>
												<Participant>
													<CorporateBody xmlns="http://reference.e-government.gv.at/namespace/persondata/20020228#">
														<Identification>
															<Value>244</Value>
															<Type>ID</Type>
														</Identification>
														<Identification>
															<Value>334556</Value>
															<Type>Firmenbuchnummer</Type>
														</Identification>
														<Identification>
															<Value>45678</Value>
															<Type>ZVR-Zahl</Type>
														</Identification>
														<Identification>
															<Value>ztue</Value>
															<Type>Kennzahl UR</Type>
														</Identification>
														<Identification>
															<Value>3</Value>
															<Type>BlockNr</Type>
														</Identification>
														<Identification>
															<Value>512</Value>
															<Type>HuellenobjektNr</Type>
														</Identification>
														<Identification>
															<Value>freigegeben</Value>
															<Type>Status</Type>
														</Identification>
														<Identification>
															<Value>Firma</Value>
															<Type>Art der Beteiligung</Type>
														</Identification>
														<Identification>
															<Value>Firma 3</Value>
															<Type>Bemerkung</Type>
														</Identification>
														<Identification>
															<Value>1</Value>
															<Type>FortlaufendeNr</Type>
														</Identification>
														<FullName>Jurperson 83 GMBH</FullName>
														<LegalForm>Gesellschaft mit beschränkter Haftung</LegalForm>
													</CorporateBody>
													<TypedPostalAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/20020228#">
														<Identification>
															<Value>Wien</Value>
															<Type>Gemeinde</Type>
														</Identification>
														<Identification>
															<Value>90001</Value>
															<Type>Katastralgemeinde</Type>
														</Identification>
														<Identification>
															<Value>536/333</Value>
															<Type>GrundstueckNr</Type>
														</Identification>
														<Type />
														<PostalAddress>
															<CountryName>Österreich</CountryName>
															<PostalCode>1078</PostalCode>
															<Hamlet>Wien</Hamlet>
															<DeliveryAddress>
																<StreetName>Waldgasse</StreetName>
																<BuildingNumber>77/84</BuildingNumber>
																<AddressRegisterEntry>
																	<AddressCode>222</AddressCode>
																	<SubCode>333</SubCode>
																	<ObjectNumber>444</ObjectNumber>
																</AddressRegisterEntry>
															</DeliveryAddress>
														</PostalAddress>
													</TypedPostalAddress>
												</Participant>
												<TypeOfParticipation>EMPFAENGER</TypeOfParticipation>
												<DispatchType>BRIEF_RSB</DispatchType>
											</Participants>
											<Locations>
												<Location>
													<TypedPostalAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/20020228#">
														<Identification>
															<Value>Wien</Value>
															<Type>Gemeinde</Type>
														</Identification>
														<Identification>
															<Value>90001</Value>
															<Type>Katastralgemeinde</Type>
														</Identification>
														<Identification>
															<Value>536/333</Value>
															<Type>GrundstueckNr</Type>
														</Identification>
														<Identification>
															<Value>-</Value>
															<Type>BlockNr</Type>
														</Identification>
														<Identification>
															<Value>-</Value>
															<Type>HuellenobjektNr</Type>
														</Identification>
														<Identification>
															<Value>in Bearbeitung</Value>
															<Type>Status</Type>
														</Identification>
														<Identification>
															<Value>Standort (in Bearb.)</Value>
															<Type>Art der Beteiligung</Type>
														</Identification>
														<Identification>
															<Value>Standort (in Bearb.)</Value>
															<Type>Bemerkung</Type>
														</Identification>
														<Type />
														<PostalAddress>
															<CountryName>Österreich</CountryName>
															<PostalCode>1078</PostalCode>
															<Hamlet>Wien</Hamlet>
															<DeliveryAddress>
																<StreetName>Waldgasse</StreetName>
																<BuildingNumber>77/87</BuildingNumber>
																<AddressRegisterEntry>
																	<AddressCode>222</AddressCode>
																	<SubCode>333</SubCode>
																	<ObjectNumber>444</ObjectNumber>
																</AddressRegisterEntry>
															</DeliveryAddress>
														</PostalAddress>
													</TypedPostalAddress>
												</Location>
												<TypeOfLocation>Standort</TypeOfLocation>
												<Description>Werk 25</Description>
											</Locations>
										</MetaData>
										<MainDocument>160323005</MainDocument>
										<Payload>
											<Layer0 id="160323005">
												<MetaData>
													<Identifier>
														<Identification />
													</Identifier>
													<ReferencedIdentifier>
														<Identification>160323005</Identification>
													</ReferencedIdentifier>
												</MetaData>
												<Payload>
													<BinaryDocument>
														<FileName>TestWord.docx</FileName>
														<MIMEType>application/vnd.openxmlformats-officedocument.wordprocessingml.document</MIMEType>
													</BinaryDocument>
												</Payload>
											</Layer0>
										</Payload>
										<BusinessType>DOKUMENTENAUSGANG</BusinessType>
										<SpecialData>
											<XMLContent>
												<GISA_Data xmlns="http://reference.e-government.gv.at/namespace/ediakt/specialdata/gisa/20140307#">
													<Natperson xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://reference.e-government.gv.at/namespace/persondata/specialdata/gisa/20140307#">
														<Uli>127</Uli>
														<Status>0</Status>
														<Nachsichten>
															<Nachsicht>
																<Umfang>Generalnachsicht 81</Umfang>
																<Fremdzahl>123/81</Fremdzahl>
																<Ausgestelltam>2014-08-21T00:00:00+02:00</Ausgestelltam>
																<Nachsichtgrund_Code>ABC81</Nachsichtgrund_Code>
																<Nachsichtgrund_Grund>langjährige Erfahrung</Nachsichtgrund_Grund>
															</Nachsicht>
														</Nachsichten>
													</Natperson>
													<Inhaber xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://reference.e-government.gv.at/namespace/persondata/specialdata/gisa/20140307#">
														<Uli>3</Uli>
														<Status>0</Status>
														<Isthistorisch>false</Isthistorisch>
														<JurpersonUli xsi:nil="true" />
														<NatpersonUli>127</NatpersonUli>
														<Endigungdatum>2014-08-21T00:00:00+02:00</Endigungdatum>
														<Endigunggrund>Verkauf</Endigunggrund>
														<Rechtswirksamab>2014-08-21T00:00:00+02:00</Rechtswirksamab>
														<Geaendertam>2014-08-21T00:00:00+02:00</Geaendertam>
														<Lmdatum>2014-08-21T00:00:00+02:00</Lmdatum>
														<Lmuser>MusterUser</Lmuser>
													</Inhaber>
													<Jurperson xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://reference.e-government.gv.at/namespace/persondata/specialdata/gisa/20140307#">
														<Uli>244</Uli>
														<Status>0</Status>
														<Nachsichten>
															<Nachsicht>
																<Umfang>Generalnachsicht 85</Umfang>
																<Fremdzahl>123/85</Fremdzahl>
																<Ausgestelltam>2014-08-21T00:00:00+02:00</Ausgestelltam>
																<Nachsichtgrund_Code>ABC85</Nachsichtgrund_Code>
																<Nachsichtgrund_Grund>langjährige Erfahrung</Nachsichtgrund_Grund>
															</Nachsicht>
														</Nachsichten>
													</Jurperson>
													<Firma xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="">
														<Uli>512</Uli>
														<Status>0</Status>
														<Isthistorisch>false</Isthistorisch>
														<InhaberUli>3</InhaberUli>
														<JurpersonUli>244</JurpersonUli>
														<Fortlaufend>1</Fortlaufend>
														<Endigungdatum>2014-08-21T00:00:00+02:00</Endigungdatum>
														<Endigunggrund>Konkurs</Endigunggrund>
														<Rechtswirksamab>2014-08-21T00:00:00+02:00</Rechtswirksamab>
														<Geaendertam>2014-08-21T00:00:00+02:00</Geaendertam>
														<Lmdatum>2014-08-21T00:00:00+02:00</Lmdatum>
														<Lmuser>MusterUser</Lmuser>
													</Firma>
													<Standort xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://reference.e-government.gv.at/namespace/persondata/specialdata/gisa/20140307#">
														<Status>1</Status>
														<Alternativebezeichnung>Werk 25</Alternativebezeichnung>
														<Endigungdatum>2014-08-21T00:00:00+02:00</Endigungdatum>
														<Endigunggrund>Verlegung</Endigunggrund>
														<Rechtswirksamab>2014-08-21T00:00:00+02:00</Rechtswirksamab>
														<Geaendertam>2014-08-21T00:00:00+02:00</Geaendertam>
														<Lmdatum>2014-08-21T00:00:00+02:00</Lmdatum>
														<Lmuser>MusterUser</Lmuser>
														<Postadresse>
															<Strassenname>Waldgasse</Strassenname>
															<Orientierungsnummer>77/87</Orientierungsnummer>
															<Postleitzahl>1078</Postleitzahl>
															<Ortschaft>Wien</Ortschaft>
															<AdressregisterAgwrAdrcd>222</AdressregisterAgwrAdrcd>
															<AdressregisterAgwrSubcd>333</AdressregisterAgwrSubcd>
															<AdressregisterAgwrObjnr>444</AdressregisterAgwrObjnr>
															<Staat>
																<Staatname>Österreich</Staatname>
																<Gwrcode>abc</Gwrcode>
																<Isocode3>AUT</Isocode3>
																<Euewr>true</Euewr>
															</Staat>
															<Gemeinde_Gemeindename>Wien</Gemeinde_Gemeindename>
															<Katastralgemeinde>90001</Katastralgemeinde>
															<Grundstuecknr>536/333</Grundstuecknr>
															<AdresseDesSchreibens>false</AdresseDesSchreibens>
														</Postadresse>
													</Standort>
												</GISA_Data>
											</XMLContent>
										</SpecialData>
									</Layer1>
								</Payload>
							</Layer2>
						</Payload>
					</Layer3>
				</Payload>
			</Ediakt>
			<Documents>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</Documents>
		</SendDataInputObject>
	</s:Body>
</s:Envelope>