<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
	<s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
		<SendDataOutputObject xmlns="http://reference.e-government.gv.at/namespace/elaktrans/3#">
			<CommonOutputParameter>
				<RelatedRequestIdentifier>
					<ConversationIdentifier>7b2fc0d5-caf3-4177-9294-69b45g774g65</ConversationIdentifier>
					<SequenceIdentifier>1</SequenceIdentifier>
				</RelatedRequestIdentifier>
				<UserMessage>
					<Message>Geschäftsstück 45-2014-8 storniert</Message>
					<Severity>INFORMATION</Severity>
				</UserMessage>
			</CommonOutputParameter>
			<Ediakt>
				<Header xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#"></Header>
				<MetaData xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">
					<Identifier>
						<Identification></Identification>
					</Identifier>
					<ReferencedIdentifier>
						<Identification></Identification>
					</ReferencedIdentifier>
				</MetaData>
				<Payload xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">
					<Layer3>
						<Subject />
						<MetaData>
							<Identifier>
								<Identification>45-2014</Identification>
							</Identifier>
							<ReferencedIdentifier>
								<Identification>45323</Identification>
							</ReferencedIdentifier>
						</MetaData>
						<Payload>
							<Layer2>
								<Subject></Subject>
								<MetaData>
									<Identifier>
										<Identification>45-2014-3</Identification>
									</Identifier>
									<ReferencedIdentifier>
										<Identification>343233</Identification>
									</ReferencedIdentifier>
								</MetaData>
								<Payload>
									<Layer1>
										<Subject></Subject>
										<MetaData>
											<Identifier>
												<Identification>45-2014-8</Identification>
											</Identifier>
											<ReferencedIdentifier>
												<Identification>54323</Identification>
											</ReferencedIdentifier>
										</MetaData>
									</Layer1>
								</Payload>
							</Layer2>
						</Payload>
					</Layer3>
				</Payload>
			</Ediakt>
		</SendDataOutputObject>
	</s:Body>
</s:Envelope>