<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
	<s:Header>
		<Action s:mustUnderstand="1" xmlns="http://schemas.microsoft.com/ws/2005/05/addressing/none">http://reference.e-government.gv.at/namespace/elaktrans/3#/SendData</Action>
	</s:Header>
	<s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
		<UnbindDataInputObject xmlns="http://reference.e-government.gv.at/namespace/elaktrans/3#">
			<CommonInputParameter>
				<SourceSystemId>at.gv.wien.testapp</SourceSystemId>
				<DestinationSystemId />
				<VKZType>L9MBA92</VKZType>
				<Procedure>WT-TST</Procedure>
				<DebugMode>True</DebugMode>
				<RequestIdentifier>
					<ConversationIdentifier>8570c460-d152-43fb-864b-dd4a8a52f4e4</ConversationIdentifier>
					<SequenceIdentifier>1</SequenceIdentifier>
				</RequestIdentifier>
			</CommonInputParameter>
			<LayerControl>
				<Layer3>
					<ReferencedIdentifier>
						<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">54321</Identification>
					</ReferencedIdentifier>
					<Layer2>
						<ReferencedIdentifier>
							<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">876</Identification>
						</ReferencedIdentifier>
						<Layer1>
							<ReferencedIdentifier>
								<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">09</Identification>
							</ReferencedIdentifier>
						</Layer1>
					</Layer2>
				</Layer3>
			</LayerControl>
		</UnbindDataInputObject>
	</s:Body>
</s:Envelope>