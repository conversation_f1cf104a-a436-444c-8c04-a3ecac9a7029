<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
	<s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
		<SendDataInputObject xmlns="http://reference.e-government.gv.at/namespace/elaktrans/3#">
			<CommonInputParameter>
				<SourceSystemId>at.gv.wien.testapp</SourceSystemId>
				<DestinationSystemId />
				<VKZType>L9MBA92</VKZType>
				<Procedure>WT-TST</Procedure>
				<RequestIdentifier>
					<ConversationIdentifier>8570c460-d152-43fb-864b-dd4a8a52f4e4</ConversationIdentifier>
					<SequenceIdentifier>1</SequenceIdentifier>
				</RequestIdentifier>
			</CommonInputParameter>
			<Purpose>OUTPUT</Purpose>
			<LayerControl>
				<Layer2SendControl>
					<ContainsModifiedBasicData>true</ContainsModifiedBasicData>
					<ContainsModifiedCatchwords>false</ContainsModifiedCatchwords>
					<ContainsModifiedLocations>false</ContainsModifiedLocations>
					<ContainsModifiedParticipants>false</ContainsModifiedParticipants>
					<ContainsModifiedState>false</ContainsModifiedState>
				</Layer2SendControl>
				<Layer1SendControl>
					<ContainsModifiedBasicData>true</ContainsModifiedBasicData>
					<ContainsModifiedCatchwords>true</ContainsModifiedCatchwords>
					<ContainsModifiedLocations>true</ContainsModifiedLocations>
					<ContainsModifiedParticipants>true</ContainsModifiedParticipants>
					<ContainsModifiedState>false</ContainsModifiedState>
				</Layer1SendControl>
				<Layer0SendControl>
					<ContainsModifiedBasicData>true</ContainsModifiedBasicData>
					<ContainsModifiedBinaryContent>true</ContainsModifiedBinaryContent>
					<ContainsModifiedCatchwords>false</ContainsModifiedCatchwords>
					<ContainsModifiedLocations>false</ContainsModifiedLocations>
					<ContainsModifiedParticipants>false</ContainsModifiedParticipants>
					<ContainsModifiedState>false</ContainsModifiedState>
				</Layer0SendControl>
				<Layer3ResponseControl />
				<Layer2ResponseControl>
					<IncludeViewUrl>true</IncludeViewUrl>
				</Layer2ResponseControl>
				<Layer1ResponseControl>
					<IncludeViewUrl>true</IncludeViewUrl>
				</Layer1ResponseControl>
				<Layer0ResponseControl>
					<IncludeViewUrl>true</IncludeViewUrl>
				</Layer0ResponseControl>
				<Layer3>
					<Identifier>
						<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">192-2014</Identification>
					</Identifier>
					<ReferencedIdentifier>
						<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">12345</Identification>
					</ReferencedIdentifier>
					<Layer2>
						<ReferencedIdentifier>
							<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">678</Identification>
						</ReferencedIdentifier>
						<Layer1>
							<ReferencedIdentifier>
								<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">90</Identification>
							</ReferencedIdentifier>
							<Layer0>
								<ReferencedIdentifier>
									<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">9876</Identification>
								</ReferencedIdentifier>
							</Layer0>
							<Layer0>
								<ReferencedIdentifier>
									<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">4677</Identification>
								</ReferencedIdentifier>
							</Layer0>
						</Layer1>
					</Layer2>
				</Layer3>
			</LayerControl>
			<Ediakt>
				<Header xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#" />
				<MetaData xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">
					<Identifier>
						<Identification />
					</Identifier>
					<ReferencedIdentifier>
						<Identification />
					</ReferencedIdentifier>
				</MetaData>
				<Payload xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">
					<Layer3>
						<Subject />
						<MetaData>
							<Identifier>
								<Identification>192-2014</Identification>
							</Identifier>
							<ReferencedIdentifier>
								<Identification>12345</Identification>
							</ReferencedIdentifier>
						</MetaData>
						<Payload>
							<Layer2>
								<Subject>Unternehmensgründung</Subject>
								<MetaData>
									<Identifier>
										<Identification />
									</Identifier>
									<ReferencedIdentifier>
										<Identification>678</Identification>
									</ReferencedIdentifier>
								</MetaData>
								<Payload>
									<Layer1>
										<Subject>Bewilligung</Subject>
										<MetaData>
											<Identifier>
												<Identification />
											</Identifier>
											<ReferencedIdentifier>
												<Identification>90</Identification>
											</ReferencedIdentifier>
											<CatchWord>Förderung A</CatchWord>
											<CatchWord>§142</CatchWord>
											<Participants>
												<Participant>
													<PhysicalPerson xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<Identification>
															<Value>2837</Value>
															<Type>ID</Type>
														</Identification>
														<Identification>
															<Value>3536210870</Value>
															<Type>Sozialversicherungsnummer</Type>
														</Identification>
														<Name>
															<GivenName>Margit</GivenName>
															<FamilyName>Müller</FamilyName>
															<Affix type="academicGrade" position="prefix">Dr.</Affix>
															<Affix type="academicGrade" position="suffix">LL.M.</Affix>
														</Name>
														<AlternativeName Type="MaidenName">
															<GivenName />
															<FamilyName>Maier</FamilyName>
														</AlternativeName>
														<MaritalStatus>married</MaritalStatus>
														<Sex>female</Sex>
														<DateOfBirth>1970-08-21</DateOfBirth>
														<PlaceOfBirth>Wien</PlaceOfBirth>
														<CountryOfBirth>Österreich</CountryOfBirth>
														<Nationality>
															<ISOCode3>AUT</ISOCode3>
															<CountryNameDE>Österreich</CountryNameDE>
															<TestApplication_Data xmlns="http://reference.e-government.gv.at/namespace/persondata/specialdata/testapplication/********#" />
														</Nationality>
														<BankConnection>
															<Holder>Margit Müller</Holder>
															<BankName>Musterbank</BankName>
															<InternationalBankConnection>
																<IBAN>BKXXAUATYY</IBAN>
																<BIC>*************************</BIC>
															</InternationalBankConnection>
														</BankConnection>
													</PhysicalPerson>
													<TypedPostalAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<Identification>
															<Value>8987</Value>
															<Type>ID</Type>
														</Identification>
														<Type>Hauptwohnsitz</Type>
														<PostalAddress>
															<CountryName>Österreich</CountryName>
															<PostalCode>1070</PostalCode>
															<Hamlet>Wien</Hamlet>
															<DeliveryAddress>
																<StreetName>Kugelgasse</StreetName>
																<BuildingNumber>44</BuildingNumber>
																<Unit>2</Unit>
																<DoorNumber>7</DoorNumber>
																<AddressRegisterEntry>
																	<AddressCode>1234567</AddressCode>
																	<SubCode>123</SubCode>
																	<ObjectNumber>1234567</ObjectNumber>
																</AddressRegisterEntry>
															</DeliveryAddress>
														</PostalAddress>
													</TypedPostalAddress>
													<TypedPostalAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<Identification>
															<Value>8543</Value>
															<Type>ID</Type>
														</Identification>
														<Identification>
															<Value>true</Value>
															<Type>AdresseDesSchreibens</Type>
														</Identification>
														<Type>Zustelladresse</Type>
														<PostalAddress>
															<CountryName>Österreich</CountryName>
															<PostalCode>1060</PostalCode>
															<Hamlet>Wien</Hamlet>
															<DeliveryAddress>
																<PostOfficeBox>386</PostOfficeBox>
															</DeliveryAddress>
														</PostalAddress>
													</TypedPostalAddress>
													<TelephoneAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<Number>
															<FormattedNumber>+43184848484</FormattedNumber>
														</Number>
													</TelephoneAddress>
													<InternetAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<Address><EMAIL></Address>
													</InternetAddress>
													<AdditionalData xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<PersonExtension xmlns="http://reference.e-government.gv.at/namespace/elaktrans/persondataext/20160401#">
															<Annotation>Einschreiben</Annotation>
															<SendingDate>2016-01-05T00:00:00+01:00</SendingDate>
														</PersonExtension>
													</AdditionalData>
												</Participant>
												<TypeOfParticipation>EMPFAENGER</TypeOfParticipation>
												<DispatchType>BRIEF_RSB</DispatchType>
												<ReportingType>zur Stellungnahme</ReportingType>
											</Participants>
											<Participants>
												<Participant>
													<CorporateBody xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<Identification>
															<Value>8447</Value>
															<Type>ID</Type>
														</Identification>
														<Identification>
															<Value>7362826</Value>
															<Type>Firmenbuchnummer</Type>
														</Identification>
														<FullName>Mega Möbel</FullName>
														<AlternativeName>Müller Möbel GmbH</AlternativeName>
														<LegalForm>Ges.m.b.H.</LegalForm>
														<Organization>Werk Stubendorf</Organization>
														<BankConnection>
															<Holder>Mega Möbel</Holder>
															<BankName>Musterbank</BankName>
															<InternationalBankConnection>
																<IBAN>BKXXAUATYY</IBAN>
																<BIC>*************************</BIC>
															</InternationalBankConnection>
														</BankConnection>
													</CorporateBody>
													<TypedPostalAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<Identification>
															<Value>4532</Value>
															<Type>ID</Type>
														</Identification>
														<Identification>
															<Value>478/22</Value>
															<Type>GrundstueckNr</Type>
														</Identification>
														<Type>Standort</Type>
														<PostalAddress>
															<CountryName>Österreich</CountryName>
															<PostalCode>3669</PostalCode>
															<State>Niederösterreich</State>
															<Hamlet>Stubendorf</Hamlet>
															<DeliveryAddress>
																<StreetName>Hauptstraße</StreetName>
																<BuildingNumber>12</BuildingNumber>
															</DeliveryAddress>
															<Recipient>
																<AdditionalText>z.H. Dr.Müller</AdditionalText>
															</Recipient>
														</PostalAddress>
													</TypedPostalAddress>
													<TelephoneAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<Number>
															<FormattedNumber>+432756464646</FormattedNumber>
														</Number>
													</TelephoneAddress>
													<TelephoneAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<Type>Fax</Type>
														<Number>
															<FormattedNumber>+43184848485</FormattedNumber>
														</Number>
													</TelephoneAddress>
												</Participant>
												<TypeOfParticipation />
											</Participants>
											<Locations>
												<Location>
													<TypedPostalAddress xmlns="http://reference.e-government.gv.at/namespace/persondata/********#">
														<Identification>
															<Value>7635</Value>
															<Type>ID</Type>
														</Identification>
														<Type />
														<PostalAddress>
															<CountryName>Österreich</CountryName>
															<PostalCode>1220</PostalCode>
															<Hamlet>Wien</Hamlet>
															<DeliveryAddress>
																<StreetName>Stadelgasse</StreetName>
																<BuildingNumber>188</BuildingNumber>
															</DeliveryAddress>
														</PostalAddress>
													</TypedPostalAddress>
												</Location>
												<TypeOfLocation>Lager</TypeOfLocation>
												<Description>Auslieferung 22. Bezirk</Description>
											</Locations>
										</MetaData>
										<MainDocument>FILE001</MainDocument>
										<Payload>
											<Layer0 id="FILE001">
												<MetaData>
													<Identifier>
														<Identification />
													</Identifier>
													<ReferencedIdentifier>
														<Identification>9876</Identification>
													</ReferencedIdentifier>
												</MetaData>
												<Payload>
													<BinaryDocument>
														<EmbeddedFileURL>FILE001</EmbeddedFileURL>
														<FileName>TestText0.txt</FileName>
														<MIMEType>text/plain</MIMEType>
													</BinaryDocument>
												</Payload>
											</Layer0>
											<Layer0 id="FILE002">
												<MetaData>
													<Identifier>
														<Identification />
													</Identifier>
													<ReferencedIdentifier>
														<Identification>4677</Identification>
													</ReferencedIdentifier>
												</MetaData>
												<Payload>
													<BinaryDocument>
														<EmbeddedFileURL>FILE002</EmbeddedFileURL>
														<FileName>TestText1.txt</FileName>
														<MIMEType>text/plain</MIMEType>
													</BinaryDocument>
												</Payload>
											</Layer0>
										</Payload>
										<BusinessType>Bescheid</BusinessType>
										<SpecialData>
											<XMLContent>
												<TestApplication_Data xmlns="http://reference.e-government.gv.at/namespace/ediakt/specialdata/testapplication/********#">
													<Person xmlns="http://reference.e-government.gv.at/namespace/ediakt/specialdata/testapplication/********#">
														<Id>2837</Id>
														<Vertretung>Anwaltskanzlei Huber</Vertretung>
													</Person>
													<Person xmlns="http://reference.e-government.gv.at/namespace/ediakt/specialdata/testapplication/********#">
														<Id>8447</Id>
														<Vertretung>Anwaltskanzlei Huber</Vertretung>
													</Person>
												</TestApplication_Data>
											</XMLContent>
										</SpecialData>
									</Layer1>
								</Payload>
							</Layer2>
						</Payload>
					</Layer3>
				</Payload>
			</Ediakt>
			<Documents>UEsDBBQAAAAIALpcAUlIA4OjBQAAAAMAAAAHAAAARklMRTAwMXN0cgYAUEsDBBQAAAAIALpcAUkLrE6WEgAAAA8AAAAHAAAARklMRTAwMnN0ctY6cu3Ok29/7msZGhkDAFBLAQIUABQAAAAIALpcAUlIA4OjBQAAAAMAAAAHAAAAAAAAAAAAAAAAAAAAAABGSUxFMDAxUEsBAhQAFAAAAAgAulwBSQusTpYSAAAADwAAAAcAAAAAAAAAAAAAAAAAKgAAAEZJTEUwMDJQSwUGAAAAAAIAAgBqAAAAYQAAAAAA</Documents>
		</SendDataInputObject>
	</s:Body>
</s:Envelope>