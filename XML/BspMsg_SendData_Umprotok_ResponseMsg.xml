<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
	<s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
		<SendDataOutputObject xmlns="http://reference.e-government.gv.at/namespace/elaktrans/3#">
			<CommonOutputParameter>
				<RelatedRequestIdentifier>
					<ConversationIdentifier>09b5b1e5-9dde-467d-9728-d0352d30c1c1</ConversationIdentifier>
					<SequenceIdentifier>1</SequenceIdentifier>
				</RelatedRequestIdentifier>
				<UserMessage>
					<Message>Geschäftsstück 09 in Geschäftsfall 712-2014-3 umprotokolliert</Message>
					<Severity>INFORMATION</Severity>
				</UserMessage>
			</CommonOutputParameter>
			<Ediakt>
				<Header xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#"></Header>
				<MetaData xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">
					<Identifier>
						<Identification></Identification>
					</Identifier>
					<ReferencedIdentifier>
						<Identification></Identification>
					</ReferencedIdentifier>
				</MetaData>
				<Payload xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">
					<Layer3>
						<Subject></Subject>
						<MetaData>
							<Identifier>
								<Identification>712-2014</Identification>
							</Identifier>
							<ReferencedIdentifier>
								<Identification>54321</Identification>
							</ReferencedIdentifier>
						</MetaData>
						<Payload>
							<Layer2>
								<Subject></Subject>
								<MetaData>
									<Identifier>
										<Identification>712-2014-3</Identification>
									</Identifier>
									<ReferencedIdentifier>
										<Identification>876</Identification>
									</ReferencedIdentifier>
								</MetaData>
								<Payload>
									<Layer1>
										<Subject></Subject>
										<MetaData>
											<Identifier>
												<Identification>712-2014-14</Identification>
											</Identifier>
											<ReferencedIdentifier>
												<Identification>09</Identification>
											</ReferencedIdentifier>
										</MetaData>
									</Layer1>
								</Payload>
							</Layer2>
						</Payload>
					</Layer3>
				</Payload>
			</Ediakt>
		</SendDataOutputObject>
	</s:Body>
</s:Envelope>