<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
	<s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
		<SendDataInputObject xmlns="http://reference.e-government.gv.at/namespace/elaktrans/3#">
			<CommonInputParameter>
				<SourceSystemId>at.gv.wien.testapp</SourceSystemId>
				<DestinationSystemId />
				<VKZType>L9MBA92</VKZType>
				<Procedure>WT-TST</Procedure>
				<RequestIdentifier>
					<ConversationIdentifier>6577a405-8df1-47af-9ac9-4cfb90eff92a</ConversationIdentifier>
					<SequenceIdentifier>1</SequenceIdentifier>
				</RequestIdentifier>
			</CommonInputParameter>
			<Purpose>OUTPUT</Purpose>
			<LayerControl>
				<Layer3SendControl>
					<ContainsModifiedBasicData>false</ContainsModifiedBasicData>
					<ContainsModifiedCatchwords>false</ContainsModifiedCatchwords>
					<ContainsModifiedLocations>false</ContainsModifiedLocations>
					<ContainsModifiedParticipants>false</ContainsModifiedParticipants>
					<ContainsModifiedState>true</ContainsModifiedState>
				</Layer3SendControl>
				<Layer3ResponseControl />
				<Layer3>
					<ReferencedIdentifier>
						<Identification xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">12345</Identification>
					</ReferencedIdentifier>
				</Layer3>
			</LayerControl>
			<Ediakt>
				<Header xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#" />
				<MetaData xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">
					<Identifier>
						<Identification />
					</Identifier>
					<ReferencedIdentifier>
						<Identification />
					</ReferencedIdentifier>
				</MetaData>
				<Payload xmlns="http://reference.e-government.gv.at/namespace/edidoc/20130808#">
					<Layer3>
						<Subject />
						<MetaData>
							<Identifier>
								<Identification>192-2014</Identification>
							</Identifier>
							<Closed>true</Closed>
							<ReferencedIdentifier>
								<Identification>12345</Identification>
							</ReferencedIdentifier>
						</MetaData>
					</Layer3>
				</Payload>
			</Ediakt>
		</SendDataInputObject>
	</s:Body>
</s:Envelope>