unit MainForm;

interface

uses
  System.SysUtils,
  System.Classes,
  Vcl.Controls,
  Vcl.Forms,
  Vcl.Dialogs,
  Vcl.StdCtrls,
  Dx.Utils.Logger;

type
  TFormMain = class(TForm)
    ButtonSelectFile: TButton;
    EditFilePath: TEdit;
    OpenDialog: TOpenDialog;
    EditBKB: TEdit;
    LabelFilePath: TLabel;
    LabelBKB: TLabel;
    ButtonSend: TButton;
    MemoLog: TMemo;
    procedure FormCreate(Sender: TObject);
    procedure ButtonSelectFileClick(Sender: TObject);
    procedure ButtonSendClick(Sender: TObject);
  private
  public
  end;

var
  FormMain: TFormMain;

implementation

{$R *.dfm}

uses
  ElakSendPDF, ElakConfig;

procedure TFormMain.FormCreate(Sender: TObject);
begin
  TDXLogger.ExternalStrings := MemoLog.Lines;
  TElakConfig.Instance;
end;

procedure TFormMain.ButtonSelectFileClick(Sender: TObject);
begin
  if OpenDialog.Execute then
    EditFilePath.Text := OpenDialog.FileName;
end;

procedure TFormMain.ButtonSendClick(Sender: TObject);
begin
  try
    if (EditFilePath.Text = '') or (EditBKB.Text = '') then
      raise Exception.Create('Bitte PDF-Datei und BKB-Nummer angeben.');

    DXLog('Sende Datei ...');
    SendPDFToElak(EditFilePath.Text, EditBKB.Text);
    DXLog('Erfolgreich gesendet.');
  except
    on E: Exception do
      DXLog('FEHLER: ' + E.Message);
  end;
end;

end.

