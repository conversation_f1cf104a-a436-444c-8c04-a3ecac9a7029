<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,Status,Aktueller Wert,Gewünschter Wert,Datenquelle,Priorität,Bemerkungen
Layer1,Subject,string,Pflicht,Kontrollbericht,,Code,Hoch,Dokumenttyp
Layer1,Title,string,Optional,,,Code,<PERSON><PERSON>rig,Zusätzlicher Titel
Layer1,MainDocument,string,Empfohlen,ABKB,,Parameter,Hoch,Verweis auf PDF
Layer1,BusinessType,string,Optional,,,Konfiguration,Mittel,Geschäftstyp/Verfahrensart
Layer1.MetaData,Identifier.Identification,string,Pflicht,ABKB,,Parameter,Hoch,BKB-Nummer
Layer1.MetaData,ReferencedIdentifier.Identification,string,Pflicht,ABKB,,Parameter,Hoch,BKB-Referenz
Layer1.MetaData,Date,DateTime,Empfohlen,,,Kontrollsystem,Hoch,Kontrolldatum
Layer1.MetaData,CreatedBy,PersonDataType,Empfoh<PERSON>,,,Benutzersystem,Ho<PERSON>,Kontrolleur
Layer1.MetaData,OrganisationalUnit,string,Empfohlen,,,Konfiguration,Hoch,Behörde
Layer1.MetaData,ActivityType,string,Optional,,,Kontrollsystem,Mittel,Kontrolltyp
Layer1.MetaData,Keyword,Array<string>,Optional,,,Kontrollart,Mittel,Schlagwörter
Layer1.MetaData,Annotation,string,Optional,,,Kontrollsystem,Niedrig,Anmerkung
Layer1.MetaData,Priority,Boolean,Optional,,,Kontrollsystem,Niedrig,Priorität
Layer1.MetaData,ObjectType,string,Optional,,,Konfiguration,Niedrig,Objekttyp
Layer1.MetaData,ParentSubject,string,Optional,,,Konfiguration,Niedrig,Übergeordneter Betreff
Layer1.MetaData,ParentIdentifier,IdentificationType,Optional,,,Kontrollsystem,Niedrig,Übergeordnete ID
Layer1.MetaData,InputAnnotation,string,Optional,,,Code,Niedrig,Eingabe-Anmerkung
Layer1.MetaData,Version,VersionType,Optional,,,Code,Niedrig,Versionsinformation
Layer1.MetaData,DueDate,DueDateType,Optional,,,Kontrollsystem,Mittel,Fälligkeitsdatum
Layer1.MetaData,Closure,Closure,Optional,,,Konfiguration,Niedrig,Verschluss/Sicherheit
Layer1.MetaData,Editor,PersonDataType,Optional,,,Benutzersystem,Mittel,Bearbeiter
Layer1.MetaData,Closed,Boolean,Optional,,,Kontrollsystem,Mittel,Geschlossen
Layer1.MetaData,Canceled,Boolean,Optional,,,Kontrollsystem,Mittel,Storniert
Layer1.MetaData,Approved,Boolean,Optional,,,Kontrollsystem,Mittel,Genehmigt
Layer1.MetaData,Suspended,Boolean,Optional,,,Kontrollsystem,Niedrig,Ausgesetzt
Layer1.MetaData,Term,Array<TermType>,Optional,,,Kontrollsystem,Mittel,Termine
Layer1.MetaData,ReceivingDate,DateTime,Optional,,,Kontrollsystem,Niedrig,Eingangsdatum
Layer1.MetaData,PhysicalObject,string,Optional,,,Konfiguration,Niedrig,Physisches Objekt
Layer1.MetaData,CassationPeriod,Integer,Optional,,,Konfiguration,Mittel,Kassationsfrist (Jahre)
Layer1.MetaData,PlannedCassationDate,Date,Optional,,,Berechnung,Mittel,Geplante Kassation
Layer1.MetaData,ArchiveDate,Date,Optional,,,Kontrollsystem,Niedrig,Archivierungsdatum
Layer1.MetaData,ArchiveAnnotation,string,Optional,,,Kontrollsystem,Niedrig,Archiv-Anmerkung
Layer1.MetaData,Receiver,Array<ReceiverType>,Optional,,,Kontrollsystem,Mittel,Empfänger
Layer1.MetaData,Sender,PersonDataType,Optional,,,Benutzersystem,Mittel,Absender
Layer1.MetaData,SendingDate,DateTime,Optional,,,System,Niedrig,Versendedatum
Layer1.MetaData,SendingAnnotation,string,Optional,,,Code,Niedrig,Versand-Anmerkung
Layer1.MetaData,SendingType,string,Optional,,,Konfiguration,Niedrig,Versandart
Layer1.MetaData,DeliveryDate,DateTime,Optional,,,System,Niedrig,Zustelldatum
Layer1.MetaData,CatchWord,Array<string>,Optional,,,Kontrollart,Niedrig,Stichwörter
Layer1.MetaData,LastChange,DateTime,Optional,,,System,Niedrig,Letzte Änderung
Layer1.MetaData,ExternalDate,DateTime,Optional,,,Kontrollsystem,Niedrig,Externes Datum
Layer1.MetaData,PostmarkDate,DateTime,Optional,,,System,Niedrig,Poststempel-Datum
Layer1.MetaData,Participants,Array<ParticipantsType>,Optional,,,Kontrollsystem,Mittel,Beteiligte Personen
Layer1.MetaData,Locations,Array<LocationsType>,Optional,,,Betriebsdaten,Mittel,Standorte
Layer1.MetaData,LastChangeBy,PersonDataType,Optional,,,Benutzersystem,Niedrig,Letzter Bearbeiter
Layer1.MetaData,SubmissionExcepted,Boolean,Optional,,,Kontrollsystem,Niedrig,Vorlage ausgenommen
Layer1.MetaData,ApprovedBy,PersonDataType,Optional,,,Benutzersystem,Mittel,Genehmiger
Layer0,id,string,Empfohlen,ABKB,,Parameter,Hoch,Dokument-ID
Layer0,Subject,string,Optional,,,Code,Niedrig,Betreff des Binärdokuments
Layer0,Title,string,Optional,,,Code,Niedrig,Titel des Binärdokuments
Layer0.MetaData,Identifier.Identification,string,Pflicht,ABKB,,Parameter,Hoch,BKB-Nummer
Layer0.MetaData,ReferencedIdentifier.Identification,string,Pflicht,ABKB,,Parameter,Hoch,BKB-Referenz
Layer0.MetaData,Date,DateTime,Empfohlen,,,Kontrollsystem,Hoch,Erstellungsdatum
Layer0.MetaData,CreatedBy,PersonDataType,Empfohlen,,,Benutzersystem,Hoch,Ersteller
Layer0.MetaData,OrganisationalUnit,string,Empfohlen,,,Konfiguration,Hoch,Behörde
Layer0.MetaData,ActivityType,string,Optional,,,Kontrollsystem,Mittel,Aktivitätstyp
Layer0.MetaData,Keyword,Array<string>,Optional,,,Kontrollart,Mittel,Schlagwörter
Layer0.MetaData,Annotation,string,Optional,,,Kontrollsystem,Niedrig,Anmerkung
Layer0.MetaData,ObjectType,string,Optional,,,Konfiguration,Niedrig,Objekttyp
Layer0.MetaData,LastChange,DateTime,Optional,,,System,Niedrig,Letzte Änderung
Layer0.MetaData,LastChangeBy,PersonDataType,Optional,,,Benutzersystem,Niedrig,Letzter Bearbeiter
PDF,EmbeddedFileURL,string,Pflicht,ABKB,,Parameter,Hoch,PDF-URL/ID
PDF,FileName,string,Pflicht,ExtractFileName(APdfPath),,Parameter,Hoch,PDF-Dateiname
PDF,MIMEType,string,Pflicht,application/pdf,,Konstante,Hoch,MIME-Type
Konfiguration,ServiceURL,string,Pflicht,http://ProductionVM:8080/ELAK,,INI-Datei,Hoch,ELAK Service URL
Konfiguration,SourceSystemId,string,Pflicht,ELKE-REST,,INI-Datei,Hoch,Quellsystem-ID
Konfiguration,DefaultProcedure,string,Pflicht,ELAK.IMPORT,,INI-Datei,Hoch,Standard-Prozedur
Konfiguration,EnableSOAPLogging,Boolean,Optional,true,,INI-Datei,Niedrig,SOAP Logging
Erweitert,Layer1_Subject,string,Optional,Kontrollbericht,,INI-Datei,Mittel,Dokumenttyp
Erweitert,Layer1_BusinessType,string,Optional,,,INI-Datei,Mittel,Geschäftstyp
Erweitert,Layer1_ObjectType,string,Optional,,,INI-Datei,Mittel,Objekttyp
Erweitert,Default_OrganisationalUnit,string,Optional,,,INI-Datei,Mittel,Standard-Organisationseinheit
Erweitert,Default_ActivityType,string,Optional,,,INI-Datei,Mittel,Standard-Aktivitätstyp
Erweitert,Default_CassationPeriod,Integer,Optional,,,INI-Datei,Mittel,Standard-Kassationsfrist
Erweitert,Default_Keywords,string,Optional,,,INI-Datei,Niedrig,Standard-Schlagwörter (kommagetrennt)
Erweitert,Default_CatchWords,string,Optional,,,INI-Datei,Niedrig,Standard-Stichwörter (kommagetrennt)
