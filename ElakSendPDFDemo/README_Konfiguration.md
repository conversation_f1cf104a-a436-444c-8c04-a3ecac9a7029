# ELAK Service Konfiguration

## Übersicht

Die ELAK Service URL ist jetzt über eine INI-Datei konfigurierbar, anstatt fest in der WSDL-Datei kodiert zu sein.

## Konfigurationsdatei

Die Konfiguration erfolgt über die Datei `ElakConfig.ini`, die im gleichen Verzeichnis wie die Anwendung platziert werden sollte.

### Beispiel ElakConfig.ini

```ini
[ELAK]
; ELAK Service Configuration
; URL des ELAK SOAP Services
ServiceURL=http://ProductionVM:8080/ELAK
; WSDL URL (falls UseWSDL=True verwendet wird)
WSDLURL=http://ProductionVM:8080/ELAK?WSDL

[System]
; System-spezifische Konfiguration
SourceSystemId=ELKE-REST
DefaultProcedure=ELAK.IMPORT

[Logging]
; Logging-Konfiguration
EnableSOAPLogging=true
```

## Konfigurationsparameter

### [ELAK] Sektion

- **ServiceURL**: Die URL des ELAK SOAP Services
  - Standard: `http://ProductionVM:8080/ELAK`
  - Beispiel für Test-Umgebung: `http://TestVM:8080/ELAK`
  - Beispiel für lokale Entwicklung: `http://localhost:8080/ELAK`

- **WSDLURL**: Die WSDL URL (wird verwendet wenn UseWSDL=True)
  - Standard: `http://ProductionVM:8080/ELAK?WSDL`

### [System] Sektion

- **SourceSystemId**: Identifikation des sendenden Systems
  - Standard: `ELKE-REST`

- **DefaultProcedure**: Standard-Prozedur für ELAK Import
  - Standard: `ELAK.IMPORT`

### [Logging] Sektion

- **EnableSOAPLogging**: Aktiviert/Deaktiviert SOAP Request/Response Logging
  - Standard: `true`
  - Werte: `true` oder `false`

## Verwendung

### In der Anwendung

```delphi
uses ElakConfig;

procedure MeineProzedur;
var
  LConfig: TElakConfig;
begin
  LConfig := TElakConfig.Instance;
  
  // Konfigurationswerte abrufen
  ShowMessage('Service URL: ' + LConfig.GetServiceURL);
end;
```

### Konfiguration zur Laufzeit ändern

1. `ElakConfig.ini` Datei bearbeiten
2. `TElakConfig.Instance.ReloadConfig` aufrufen

## Deployment

### Produktionsumgebung
```ini
[ELAK]
ServiceURL=http://ProductionVM:8080/ELAK
```

### Testumgebung
```ini
[ELAK]
ServiceURL=http://TestVM:8080/ELAK
```

### Entwicklungsumgebung
```ini
[ELAK]
ServiceURL=http://localhost:8080/ELAK
EnableSOAPLogging=true
```

## Fehlerbehandlung

- Falls `ElakConfig.ini` nicht gefunden wird, werden Standardwerte verwendet
- Fehlende Konfigurationsparameter werden durch Standardwerte ersetzt
- Fehler werden über das DX.Utils.Logger System protokolliert

## Migration von der alten Konfiguration

Die alte fest kodierte URL in `ELAK_WSDL.pas` wird jetzt ignoriert. Die neue Konfiguration überschreibt diese Werte zur Laufzeit.

### Vorher (fest kodiert):
```delphi
LWS := GetElakTrans(False, '', LHTTPRIO);  // Verwendet fest kodierte URL
```

### Nachher (konfigurierbar):
```delphi
LConfig := TElakConfig.Instance;
LServiceURL := LConfig.GetServiceURL;
LWS := GetElakTrans(False, LServiceURL, LHTTPRIO);  // Verwendet konfigurierte URL
```
