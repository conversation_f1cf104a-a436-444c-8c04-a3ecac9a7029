# ELAK Layer-Struktur Vereinfachung

## Übersicht

Die ELAK-Implementierung wurde vereinfacht, um nur Layer 1 und Layer 0 zu verwenden. Layer 3 und Layer 2 werden übersprungen.

## Vorher (mit allen Layern)

```
EdiaktType.Payload (Payload4)
├── Layer3 (Array_Of_Layer3Type)
│   └── Layer3Type
│       └── Payload (Payload5)
│           └── Layer2 (Array_Of_Layer2Type)
│               └── Layer2Type
│                   └── Payload (Payload2)
│                       └── Layer1 (Array_Of_Layer1Type)
│                           └── Layer1Type
│                               └── Payload (Payload3)
│                                   └── Layer0 (Array_Of_Layer0Type)
│                                       └── Layer0Type (PDF-Dokument)
```

## Nachher (vereinfacht)

```
EdiaktType.Payload (Payload4)
└── Layer1 (Payload2)
    └── Layer1Type
        └── Payload (Payload3)
            └── Layer0 (Array_Of_Layer0Type)
                └── Layer0Type (PDF-Dokument)
```

## Implementierung

### Payload-Struktur

```delphi
// EdiaktType.Payload ist Payload4, welches Layer1 property vom Typ Payload2 hat
LPayload4 := Payload4.Create;
LPayload2 := Payload2.Create;
LLayer1 := Layer1Type.Create;

// Layer1 Array erstellen
SetLength(LPayload2, 1);
LPayload2[0] := LLayer1;

// Layer1 zu Payload4 zuweisen und dann zu EdiaktType
LPayload4.Layer1 := LPayload2;
LInput.Ediakt.Payload := LPayload4;
```

### Layer-Konfiguration

```delphi
// Layer 1 (Dokument)
LLayer1.Subject := 'Kontrollbericht';
LLayer1.MetaData := MetaDataType.Create;
LLayer1.MetaData.Identifier := IdentificationType.Create;
LLayer1.MetaData.ReferencedIdentifier := IdentificationType.Create;
LLayer1.MetaData.ReferencedIdentifier.Identification := ABKB;
LLayer1.MainDocument := LDocID;

// Layer 0 (Binärdokument)
LLayer0.Id := LDocID;
LLayer0.MetaData := MetaDataType.Create;
LLayer0.MetaData.Identifier := IdentificationType.Create;
LLayer0.MetaData.ReferencedIdentifier := IdentificationType.Create;
LLayer0.MetaData.ReferencedIdentifier.Identification := ABKB;
LLayer0.Payload := PayloadType.Create;
LLayer0.Payload.BinaryDocument := BinaryDocument.Create;
LLayer0.Payload.BinaryDocument.EmbeddedFileURL := LDocID;
LLayer0.Payload.BinaryDocument.FileName := ExtractFileName(APdfPath);
LLayer0.Payload.BinaryDocument.MIMEType := 'application/pdf';
```

### LayerControl-Einstellungen

```delphi
// Nur Layer1 und Layer0 werden konfiguriert
LInput.LayerControl := LayerControlType.Create;

// Layer 1 (Dokument) control settings
LInput.LayerControl.Layer1SendControl := LayerSendControlType.Create;
LInput.LayerControl.Layer1SendControl.ContainsModifiedBasicData := True;

// Layer 0 (Binärdokument) control settings
LInput.LayerControl.Layer0SendControl := LayerSendControlType.Create;
LInput.LayerControl.Layer0SendControl.ContainsModifiedBasicData := True;
LInput.LayerControl.Layer0SendControl.ContainsModifiedBinaryContent := True;
```

## Vorteile der Vereinfachung

### 1. Weniger Komplexität
- Weniger Objekte zu erstellen und verwalten
- Einfachere Datenstruktur
- Reduzierte Fehlerquellen

### 2. Bessere Performance
- Weniger Speicherverbrauch
- Schnellere Objekterstellung
- Weniger SOAP-Overhead

### 3. Einfachere Wartung
- Weniger Code zu pflegen
- Klarere Struktur
- Einfachere Debugging

### 4. Fokus auf Wesentliches
- Direkte Zuordnung: Kontrollbericht (Layer1) → PDF-Dokument (Layer0)
- Keine unnötigen Zwischenschichten
- Klare Semantik

## ELAK-Kompatibilität

Die vereinfachte Struktur ist vollständig ELAK-kompatibel, da:

1. **XSD-Schema unterstützt Choice**: Das EdiaktType.Payload unterstützt eine Auswahl zwischen Layer3, Layer2 oder Layer1
2. **Optionale Layer**: Layer3 und Layer2 sind optional und können weggelassen werden
3. **Vollständige Metadaten**: Alle erforderlichen Metadaten werden auf Layer1 und Layer0 gesetzt

## Semantische Bedeutung

### Layer 1 (Dokument-Ebene)
- **Subject**: "Kontrollbericht"
- **Identification**: BKB-Nummer (Betriebskontrollbuch)
- **MainDocument**: Verweis auf das Haupt-PDF-Dokument

### Layer 0 (Binärdokument-Ebene)
- **Id**: Eindeutige Dokument-ID (BKB-Nummer)
- **Identification**: BKB-Nummer für Referenzierung
- **BinaryDocument**: Das eigentliche PDF-Dokument mit Metadaten

## Migration

### Von der alten Struktur:
```delphi
// Alte Implementierung mit Layer3 und Layer2
LLayer3 := Layer3Type.Create;
LLayer2 := Layer2Type.Create;
LLayer1 := Layer1Type.Create;
// ... komplexe Verschachtelung
```

### Zur neuen Struktur:
```delphi
// Neue vereinfachte Implementierung
LPayload4 := Payload4.Create;
LPayload2 := Payload2.Create;
LLayer1 := Layer1Type.Create;
LPayload4.Layer1 := LPayload2;
```

## Fazit

Die Vereinfachung auf Layer 1 und Layer 0 bietet eine saubere, effiziente und wartbare Lösung für die ELAK-Integration, ohne die Funktionalität oder Kompatibilität zu beeinträchtigen.
