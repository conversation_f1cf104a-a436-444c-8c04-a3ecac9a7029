unit ElakSendPDF;

interface

uses
  System.SysUtils,
  System.Classes,
  System.Generics.Collections,
  System.NetEncoding,
  System.Zip,
  System.IOUtils,

  Soap.InvokeRegistry,
  Soap.Rio,
  Soap.SOAPHTTPClient,

  DX.Utils.Logger,

//  ElakTrans_SendData_TLB;
ELAK_WSDL;

type
  TSOAPLogger = class
    class procedure LogRequest(const MethodName: string; RequestStream: TStream);
    class procedure LogResponse(const MethodName: string; ResponseStream: TStream);
  end;

procedure SendPDFToElak(const APdfPath:string; ABKB: string);

implementation

function EncodeZipWithPDF(const APDFPath, ADocID: string): string;
var
  LZipStream: TMemoryStream;
  LZip: TZipFile;
  LEncoded: TBytes;
begin
  LZipStream := TMemoryStream.Create;
  try
    LZip := TZipFile.Create;
    try
      LZip.Open(LZipStream, zmWrite);
      LZip.Add(APDFPath, ADocID + ExtractFileExt(APDFPath));
      LZip.Close;
    finally
      LZip.Free;
    end;
    LZipStream.Position := 0;
    SetLength(LEncoded, LZipStream.Size);
    LZipStream.ReadBuffer(LEncoded[0], LZipStream.Size);
    Result := TNetEncoding.Base64.EncodeBytesToString(LEncoded);
  finally
    LZipStream.Free;
  end;
end;

procedure SendPDFToElak(const APdfPath:string; ABKB: string);
var
  LHTTPRIO: THTTPRIO;
  LWS: ElakTrans;
  LInput: SendDataInputObject;
  LDocID: string;
  LLayer3: Layer3Type;
  LLayer2: Layer2Type;
  LLayer1: Layer1Type;
  LLayer0: Layer0Type;
begin
  LDocID := '';
  LHTTPRIO := nil;
  LInput := nil;
  LLayer0 := nil;
  LLayer1 := nil;
  LLayer2 := nil;
  LLayer3 := nil;

  LHTTPRIO := THTTPRIO.Create(nil);

  LHTTPRIO.OnBeforeExecute := TSOAPLogger.LogRequest;
  LHTTPRIO.OnAfterExecute := TSOAPLogger.LogResponse;

  LWS := GetElakTrans(False, '', LHTTPRIO);
  LInput := SendDataInputObject.Create;
  try
    LInput.CommonInputParameter := CommonInputParameterType.Create;
    with LInput.CommonInputParameter do
    begin
      SourceSystemId := 'ELKE-REST';
      VKZType := 'VKZ123';
      Procedure_ := 'TESTPROC';
      RequestIdentifier := RequestIdentifierType.Create;
      RequestIdentifier.ConversationIdentifier := TGUID.NewGuid.ToString;
      RequestIdentifier.SequenceIdentifier := '1';
    end;

    LInput.Purpose := PurposeType.INPUT;

    LInput.LayerControl := LayerControlType.Create;
    LInput.LayerControl.Layer3SendControl := Layer3ControlType.Create;
    LInput.LayerControl.Layer3SendControl.ContainsModifiedBasicData := True;

    LInput.LayerControl.Layer2SendControl := Layer2ControlType.Create;
   LInput.LayerControl.Layer2SendControl.ContainsModifiedBasicData := True;

    LInput.LayerControl.Layer1SendControl := Layer1ControlType.Create;
   LInput.LayerControl.Layer1SendControl.ContainsModifiedBasicData := True;

    LInput.LayerControl.Layer0SendControl := Layer0ControlType.Create;
    LInput.LayerControl.Layer0SendControl.ContainsModifiedBasicData := True;
   LInput.LayerControl.Layer0SendControl.ContainsModifiedBinaryContent := True;

    LDocID := ABKB;
    LInput.Documents := EncodeZipWithPDF(APdfPath, LDocID);

    LInput.Ediakt := EdiaktType.Create;
    with LInput.Ediakt do
    begin
      Header := HeaderType.Create;
      MetaData := MetaDataType.Create;
      MetaData.Identifier := IdentificationType.Create;
      MetaData.ReferencedIdentifier := IdentificationType.Create;

      Payload := PayloadType.Create;
      LLayer3 := Layer3Type.Create;
      Payload.Layer3 := LLayer3;
      with LLayer3 do
      begin
        Subject := 'Kontrollakt zu ' + ABKB;
        MetaData := MetaDataType.Create;
        MetaData.Identifier := IdentificationType.Create;
        MetaData.ReferencedIdentifier := IdentificationType.Create;
        MetaData.ReferencedIdentifier.Identification := 'L3-' + ABKB;

        LLayer2 := Layer2Type.Create;
        LLayer3.Payload := LLayer2;
        LLayer2.Subject := 'Kontrollfall';
        LLayer2.MetaData := MetaDataType.Create;
        LLayer2.MetaData.Identifier := IdentificationType.Create;
        LLayer2.MetaData.ReferencedIdentifier := IdentificationType.Create;
        LLayer2.MetaData.ReferencedIdentifier.Identification := 'L2-' + ABKB;

        LLayer1 := Layer1Type.Create;
        LLayer2.Payload := LLayer1;
        LLayer1.Subject := 'Kontrollbericht';
        LLayer1.MetaData := MetaDataType.Create;
        LLayer1.MetaData.Identifier := IdentificationType.Create;
        LLayer1.MetaData.ReferencedIdentifier := IdentificationType.Create;
        LLayer1.MetaData.ReferencedIdentifier.Identification := 'L1-' + ABKB;
        LLayer1.MainDocument := LDocID;

        LLayer1.Payload := TObjectList<Layer0Type>.Create(True);

        LLayer0 := Layer0Type.Create;
        LLayer0.Id := LDocID;
        LLayer0.MetaData := MetaDataType.Create;
        LLayer0.MetaData.Identifier := IdentificationType.Create;
        LLayer0.MetaData.ReferencedIdentifier := IdentificationType.Create;
        LLayer0.MetaData.ReferencedIdentifier.Identification := ABKB;
        LLayer0.Payload := PayloadType2.Create;
        LLayer0.Payload.BinaryDocument := BinaryDocumentType.Create;
        LLayer0.Payload.BinaryDocument.EmbeddedFileURL := LDocID;
        LLayer0.Payload.BinaryDocument.FileName := ExtractFileName(APdfPath);
        LLayer0.Payload.BinaryDocument.MIMEType := 'application/pdf';

        LLayer1.Payload.Add(LLayer0);
      end;
    end;

    try
      LWS.SendData(LInput);
    except
      on E: Exception do
        raise Exception.Create('Fehler beim Senden an ELAK: ' + E.Message);
    end;
  finally
    if Assigned(LLayer1) then
      LLayer1.Payload.Free;
    FreeAndNil(LLayer0);
    FreeAndNil(LLayer1);
    FreeAndNil(LLayer2);
    FreeAndNil(LLayer3);
    FreeAndNil(LInput);
    FreeAndNil(LHTTPRIO);
  end;
end;

class procedure TSOAPLogger.LogRequest(const MethodName: string; RequestStream: TStream);
var
  LStrings: TStringList;
begin
  LStrings := TStringList.Create;
  try
    RequestStream.Position := 0;
    LStrings.LoadFromStream(RequestStream, TEncoding.UTF8);
    DXLog(LStrings.Text);
  finally
    LStrings.Free;
  end;
end;

class procedure TSOAPLogger.LogResponse(const MethodName: string; ResponseStream: TStream);
var
  LStrings: TStringList;
begin
  LStrings := TStringList.Create;
  try
    ResponseStream.Position := 0;
    LStrings.LoadFromStream(ResponseStream, TEncoding.UTF8);
    DXLog(LStrings.Text);
  finally
    LStrings.Free;
  end;
end;

end.

