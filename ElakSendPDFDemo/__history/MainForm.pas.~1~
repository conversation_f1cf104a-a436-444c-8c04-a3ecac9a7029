unit MainForm;

interface

uses
  System.SysUtils,
  System.Classes,
  Vcl.Controls,
  Vcl.Forms,
  Vcl.Dialogs,
  Vcl.StdCtrls;

type
  TFormMain = class(TForm)
    ButtonSelectFile: TButton;
    EditFilePath: TEdit;
    OpenDialog: TOpenDialog;
    EditBKB: TEdit;
    LabelFilePath: TLabel;
    LabelBKB: TLabel;
    ButtonSend: TButton;
    MemoLog: TMemo;
    procedure ButtonSelectFileClick(Sender: TObject);
    procedure ButtonSendClick(Sender: TObject);
  private
    procedure Log(const AMsg: string);
  public
  end;

var
  FormMain: TFormMain;

implementation

{$R *.dfm}

uses
  ElakSendPDF;

procedure TFormMain.ButtonSelectFileClick(Sender: TObject);
begin
  if OpenDialog.Execute then
    EditFilePath.Text := OpenDialog.FileName;
end;

procedure TFormMain.ButtonSendClick(Sender: TObject);
begin
  try
    if (EditFilePath.Text = '') or (EditBKB.Text = '') then
      raise Exception.Create('Bitte PDF-Datei und BKB-Nummer angeben.');

    Log('Sende Datei ...');
    SendPDFToElak(EditFilePath.Text, EditBKB.Text);
    Log('Erfolgreich gesendet.');
  except
    on E: Exception do
      Log('FEHLER: ' + E.Message);
  end;
end;

procedure TFormMain.Log(const AMsg: string);
begin
  MemoLog.Lines.Add(FormatDateTime('hh:nn:ss', Now) + ' - ' + AMsg);
end;

end.
