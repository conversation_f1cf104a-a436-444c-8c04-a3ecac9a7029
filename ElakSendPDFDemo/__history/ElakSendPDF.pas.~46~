﻿unit ElakSendPDF;

interface

uses
  System.SysUtils,
  System.Classes,
  System.Generics.Collections,
  System.NetEncoding,
  System.Zip,
  System.IOUtils,

  Soap.InvokeRegistry,
  Soap.Rio,
  Soap.SOAPHTTPClient,

  DX.Utils.Logger,

//  ElakTrans_SendData_TLB;
ELAK_WSDL;

type
  TSOAPLogger = class
    class procedure LogRequest(const MethodName: string; RequestStream: TStream);
    class procedure LogResponse(const MethodName: string; ResponseStream: TStream);
  end;

procedure SendPDFToElak(const APdfPath:string; ABKB: string);

implementation

function EncodeZipWithPDF(const APDFPath, ADocID: string): TArray<System.Byte>;
var
  LZipStream: TMemoryStream;
  LZip: TZipFile;
  LEncoded: TBytes;
begin
  LZipStream := TMemoryStream.Create;
  try
    LZip := TZipFile.Create;
    try
      LZip.Open(LZipStream, zmWrite);
      LZip.Add(APDFPath, ADocID + ExtractFileExt(APDFPath));
      LZip.Close;
    finally
      LZip.Free;
    end;
    LZipStream.Position := 0;
    SetLength(LEncoded, LZipStream.Size);
    LZipStream.ReadBuffer(LEncoded[0], LZipStream.Size);
    Result := LEncoded;
  finally
    LZipStream.Free;
  end;
end;

procedure SendPDFToElak(const APdfPath:string; ABKB: string);
var
  LHTTPRIO: THTTPRIO;
  LWS: ElakTrans;
  LInput: SendDataInputObject;
  LDocID: string;
  LLayer3: Layer3Type;
  LLayer2: Layer2Type;
  LLayer1: Layer1Type;
  LLayer0: Layer0Type;
  FLayer3Array: Array_Of_Layer3Type;
  FLayer2Array: Array_Of_Layer2Type;
  FLayer1Array: Array_Of_Layer1ControlType;
  FLayer0Array: Array_Of_Layer0ControlType;
  LPayload2: Payload2;
  LPayload3: Payload3;
  LPayload4: Payload4;
  LPayload5: Payload5;
begin
  LDocID := '';
  LHTTPRIO := nil;
  LInput := nil;
  LLayer0 := nil;
  LLayer1 := nil;
  LLayer2 := nil;
  LLayer3 := nil;

  LHTTPRIO := THTTPRIO.Create(nil);

  LHTTPRIO.OnBeforeExecute := TSOAPLogger.LogRequest;
  LHTTPRIO.OnAfterExecute := TSOAPLogger.LogResponse;

  LWS := GetElakTrans(False, '', LHTTPRIO);
  LInput := SendDataInputObject.Create;
  try
    // Initialize CommonInputParameter with required values
    LInput.CommonInputParameter := CommonInputParameterType.Create;
    LInput.CommonInputParameter.SourceSystemId := 'ELKE-REST';  // Should be configured according to your system
    LInput.CommonInputParameter.VKZType := 'VKZ123';  // Should be a valid VKZ (Verfahrenskennzahl)
    LInput.CommonInputParameter.Procedure_ := 'ELAK.IMPORT';  // Standard procedure for importing documents

    // Set up request identifier with a new GUID and sequence number
    LInput.CommonInputParameter.RequestIdentifier := RequestIdentifierType.Create;
    LInput.CommonInputParameter.RequestIdentifier.ConversationIdentifier := TGUID.NewGuid.ToString;
    LInput.CommonInputParameter.RequestIdentifier.SequenceIdentifier := '1';

    // Set the purpose of the request
    LInput.Purpose := PurposeType.INPUT;

    // Configure layer controls to specify what data we're sending
    LInput.LayerControl := LayerControlType.Create;

    // Layer 3 (Akt) control settings
    LInput.LayerControl.Layer3SendControl := LayerSendControlType.Create;
    LInput.LayerControl.Layer3SendControl.ContainsModifiedBasicData := True;

    // Layer 2 (Vorgang) control settings
    LInput.LayerControl.Layer2SendControl := LayerSendControlType.Create;
    LInput.LayerControl.Layer2SendControl.ContainsModifiedBasicData := True;

    // Layer 1 (Dokument) control settings
    LInput.LayerControl.Layer1SendControl := LayerSendControlType.Create;
    LInput.LayerControl.Layer1SendControl.ContainsModifiedBasicData := True;

    // Layer 0 (Binärdokument) control settings
    LInput.LayerControl.Layer0SendControl := LayerSendControlType.Create;
    LInput.LayerControl.Layer0SendControl.ContainsModifiedBasicData := True;
    LInput.LayerControl.Layer0SendControl.ContainsModifiedBinaryContent := True;

    LDocID := ABKB;
    LInput.Documents := EncodeZipWithPDF(APdfPath, LDocID);

    LInput.Ediakt := EdiaktType.Create;
    LInput.Ediakt.Header := HeaderType.Create;
    LInput.Ediakt.MetaData := MetaDataType.Create;
    LInput.Ediakt.MetaData.Identifier := IdentificationType.Create;
    LInput.Ediakt.MetaData.ReferencedIdentifier := IdentificationType.Create;

    // Initialize Payload which is of type Payload4 containing Layer3Type
    LLayer3 := Layer3Type.Create;
    // Create and assign the payload
    LPayload4 := Payload4.Create;
    // Set the Layer3 array with one element
    SetLength(FLayer3Array, 1);
    FLayer3Array[0] := LLayer3;
    LPayload4.Layer3 := FLayer3Array;
    LInput.Ediakt.Payload := LPayload4;

    // Configure Layer3
    LLayer3.Subject := 'Kontrollakt zu ' + ABKB;
    LLayer3.MetaData := MetaDataType.Create;
    LLayer3.MetaData.Identifier := IdentificationType.Create;
    LLayer3.MetaData.ReferencedIdentifier := IdentificationType.Create;
    LLayer3.MetaData.ReferencedIdentifier.Identification := 'L3-' + ABKB;

    // Initialize Payload which is of type Payload5 containing Layer2Type
    LLayer2 := Layer2Type.Create;
    // Create and assign the payload
    LPayload5 := Payload5.Create;
    // Set the Layer2 array with one element
    SetLength(FLayer2Array, 1);
    FLayer2Array[0] := LLayer2;
    LPayload5.Layer2 := FLayer2Array;
    LLayer3.Payload := LPayload5;

    // Configure Layer2
    LLayer2.Subject := 'Kontrollfall';
    LLayer2.MetaData := MetaDataType.Create;
    LLayer2.MetaData.Identifier := IdentificationType.Create;
    LLayer2.MetaData.ReferencedIdentifier := IdentificationType.Create;
    LLayer2.MetaData.ReferencedIdentifier.Identification := 'L2-' + ABKB;

    // Initialize Payload which is of type Payload2 containing Layer1Type
    LLayer1 := Layer1Type.Create;
    // Create and assign the payload
    LPayload2 := Payload2.Create;
    // Set the Layer1 array with one element
    SetLength(FLayer1Array, 1);
    FLayer1Array[0] := LLayer1;
    LPayload2.Layer1 := FLayer1Array;
    LLayer2.Payload := LPayload2;

    // Configure Layer1
    LLayer1.Subject := 'Kontrollbericht';
    LLayer1.MetaData := MetaDataType.Create;
    LLayer1.MetaData.Identifier := IdentificationType.Create;
    LLayer1.MetaData.ReferencedIdentifier := IdentificationType.Create;
    LLayer1.MetaData.ReferencedIdentifier.Identification := 'L1-' + ABKB;
    LLayer1.MainDocument := LDocID;

    // Initialize Payload3 which is an array of Layer0Type
    LLayer0 := Layer0Type.Create;
    // Create and assign the payload as an array with one element
    SetLength(LPayload3, 1);
    LPayload3[0] := LLayer0;
    LLayer1.Payload := LPayload3;

    // Configure Layer0
    LLayer0.Id := LDocID;
    LLayer0.MetaData := MetaDataType.Create;
    LLayer0.MetaData.Identifier := IdentificationType.Create;
    LLayer0.MetaData.ReferencedIdentifier := IdentificationType.Create;
    LLayer0.MetaData.ReferencedIdentifier.Identification := ABKB;
    LLayer0.Payload := PayloadType2.Create;
    LLayer0.Payload.BinaryDocument := BinaryDocumentType.Create;
    LLayer0.Payload.BinaryDocument.EmbeddedFileURL := LDocID;
    LLayer0.Payload.BinaryDocument.FileName := ExtractFileName(APdfPath);
    LLayer0.Payload.BinaryDocument.MIMEType := 'application/pdf';

    try
      LWS.SendData(LInput);
    except
      on E: Exception do
        raise Exception.Create('Fehler beim Senden an ELAK: ' + E.Message);
    end;
  finally
    // Clean up objects in reverse order of creation
    if Assigned(LLayer1) and Assigned(LLayer1.Payload) then
      LLayer1.Payload.Free;
    FreeAndNil(LLayer0);
    FreeAndNil(LLayer1);
    FreeAndNil(LLayer2);
    FreeAndNil(LLayer3);
    FreeAndNil(LInput);
    FreeAndNil(LHTTPRIO);
  end;
end;

class procedure TSOAPLogger.LogRequest(const MethodName: string; RequestStream: TStream);
var
  LStrings: TStringList;
begin
  LStrings := TStringList.Create;
  try
    RequestStream.Position := 0;
    LStrings.LoadFromStream(RequestStream, TEncoding.UTF8);
    DXLog(LStrings.Text);
  finally
    LStrings.Free;
  end;
end;

class procedure TSOAPLogger.LogResponse(const MethodName: string; ResponseStream: TStream);
var
  LStrings: TStringList;
begin
  LStrings := TStringList.Create;
  try
    ResponseStream.Position := 0;
    LStrings.LoadFromStream(ResponseStream, TEncoding.UTF8);
    DXLog(LStrings.Text);
  finally
    LStrings.Free;
  end;
end;

end.

