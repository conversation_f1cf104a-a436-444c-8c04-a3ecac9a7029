unit ElakTrans_SendData_TLB;

interface

uses
  Soap.InvokeRegistry, Soap.Rio, Soap.SOAPHTTPClient, System.Types, System.Classes, System.Generics.Collections;

const
  IS_OPTN = $0001;
  IS_UNQL = $0008;

type
  SendDataInputObject = class;
  CommonInputParameterType = class;
  RequestIdentifierType = class;
  EdiaktType = class;
  HeaderType = class;
  MetaDataType = class;
  IdentificationType = class;
  PayloadType = class;
  Layer3Type = class;
  Layer2Type = class;
  Layer1Type = class;
  Layer0Type = class;
  PayloadType2 = class;
  BinaryDocumentType = class;

  FIS2ELAKPortType = interface(IInvokable)
    ['{96E5E002-A980-4F3B-8FC3-6B5E9E5FDD77}']
    function SendData(const input: SendDataInputObject): string; stdcall;
  end;

  RequestIdentifierType = class(TRemotable)
  private
    FConversationIdentifier: string;
    FSequenceIdentifier: Integer;
  published
    property ConversationIdentifier: string read FConversationIdentifier write FConversationIdentifier;
    property SequenceIdentifier: Integer read FSequenceIdentifier write FSequenceIdentifier;
  end;

  CommonInputParameterType = class(TRemotable)
  private
    FSourceSystemId: string;
    FDestinationSystemId: string;
    FVKZType: string;
    FProcedure: string;
    FRequestIdentifier: RequestIdentifierType;
  published
    property SourceSystemId: string read FSourceSystemId write FSourceSystemId;
    property DestinationSystemId: string read FDestinationSystemId write FDestinationSystemId;
    property VKZType: string read FVKZType write FVKZType;
    property Procedure_: string read FProcedure write FProcedure;
    property RequestIdentifier: RequestIdentifierType read FRequestIdentifier write FRequestIdentifier;
  end;

  IdentificationType = class(TRemotable)
  private
    FIdentification: string;
  published
    property Identification: string read FIdentification write FIdentification;
  end;

  MetaDataType = class(TRemotable)
  private
    FIdentifier: IdentificationType;
    FReferencedIdentifier: IdentificationType;
  published
    property Identifier: IdentificationType read FIdentifier write FIdentifier;
    property ReferencedIdentifier: IdentificationType read FReferencedIdentifier write FReferencedIdentifier;
  end;

  HeaderType = class(TRemotable)
  end;

  BinaryDocumentType = class(TRemotable)
  private
    FEmbeddedFileURL: string;
    FFileName: string;
    FMIMEType: string;
  published
    property EmbeddedFileURL: string read FEmbeddedFileURL write FEmbeddedFileURL;
    property FileName: string read FFileName write FFileName;
    property MIMEType: string read FMIMEType write FMIMEType;
  end;

  PayloadType2 = class(TRemotable)
  private
    FBinaryDocument: BinaryDocumentType;
  published
    property BinaryDocument: BinaryDocumentType read FBinaryDocument write FBinaryDocument;
  end;

  Layer0Type = class(TRemotable)
  private
    FId: string;
    FMetaData: MetaDataType;
    FPayload: PayloadType2;
  published
    property Id: string read FId write FId;
    property MetaData: MetaDataType read FMetaData write FMetaData;
    property Payload: PayloadType2 read FPayload write FPayload;
  end;

  Layer1Type = class(TRemotable)
  private
    FSubject: string;
    FMetaData: MetaDataType;
    FMainDocument: string;
    FPayload: TObjectList<Layer0Type>;
  public
    constructor Create; override;
    destructor Destroy; override;
  published
    property Subject: string read FSubject write FSubject;
    property MetaData: MetaDataType read FMetaData write FMetaData;
    property MainDocument: string read FMainDocument write FMainDocument;
    property Payload: TObjectList<Layer0Type> read FPayload write FPayload;
  end;

  Layer2Type = class(TRemotable)
  private
    FSubject: string;
    FMetaData: MetaDataType;
    FPayload: Layer1Type;
  published
    property Subject: string read FSubject write FSubject;
    property MetaData: MetaDataType read FMetaData write FMetaData;
    property Payload: Layer1Type read FPayload write FPayload;
  end;

  Layer3Type = class(TRemotable)
  private
    FSubject: string;
    FMetaData: MetaDataType;
    FPayload: Layer2Type;
  published
    property Subject: string read FSubject write FSubject;
    property MetaData: MetaDataType read FMetaData write FMetaData;
    property Payload: Layer2Type read FPayload write FPayload;
  end;

  PayloadType = class(TRemotable)
  private
    FLayer3: Layer3Type;
  published
    property Layer3: Layer3Type read FLayer3 write FLayer3;
  end;

  EdiaktType = class(TRemotable)
  private
    FHeader: HeaderType;
    FMetaData: MetaDataType;
    FPayload: PayloadType;
  published
    property Header: HeaderType read FHeader write FHeader;
    property MetaData: MetaDataType read FMetaData write FMetaData;
    property Payload: PayloadType read FPayload write FPayload;
  end;

  SendDataInputObject = class(TRemotable)
  private
    FCommonInputParameter: CommonInputParameterType;
    FPurpose: string;
    FDocuments: string;
    FLayerControl: TObject;
    FEdiakt: EdiaktType;
  published
    property CommonInputParameter: CommonInputParameterType read FCommonInputParameter write FCommonInputParameter;
    property Purpose: string read FPurpose write FPurpose;
    property Documents: string read FDocuments write FDocuments;
    property LayerControl: TObject read FLayerControl write FLayerControl;
    property Ediakt: EdiaktType read FEdiakt write FEdiakt;
  end;


  Layer0SendControlType = class(TRemotable)
  private
    FContainsModifiedBasicData: Boolean;
    FContainsModifiedBinaryContent: Boolean;
  published
    property ContainsModifiedBasicData: Boolean read FContainsModifiedBasicData write FContainsModifiedBasicData;
    property ContainsModifiedBinaryContent: Boolean read FContainsModifiedBinaryContent write FContainsModifiedBinaryContent;
  end;

  Layer1SendControlType = class(TRemotable)
  private
    FContainsModifiedBasicData: Boolean;
  published
    property ContainsModifiedBasicData: Boolean read FContainsModifiedBasicData write FContainsModifiedBasicData;
  end;

  Layer2SendControlType = class(TRemotable)
  private
    FContainsModifiedBasicData: Boolean;
  published
    property ContainsModifiedBasicData: Boolean read FContainsModifiedBasicData write FContainsModifiedBasicData;
  end;

  Layer3SendControlType = class(TRemotable)
  private
    FContainsModifiedBasicData: Boolean;
  published
    property ContainsModifiedBasicData: Boolean read FContainsModifiedBasicData write FContainsModifiedBasicData;
  end;

  
  LayerControlType = class(TRemotable)
  private
    FLayer0: Layer0SendControlType;
    FLayer1: Layer1SendControlType;
    FLayer2: Layer2SendControlType;
    FLayer3: Layer3SendControlType;
  published
    property Layer0: Layer0SendControlType read FLayer0 write FLayer0;
    property Layer1: Layer1SendControlType read FLayer1 write FLayer1;
    property Layer2: Layer2SendControlType read FLayer2 write FLayer2;
    property Layer3: Layer3SendControlType read FLayer3 write FLayer3;
  end;



function GetFIS2ELAKPortType( = True; Addr: string = ''; HTTPRIO: THTTPRIO = nil): FIS2ELAKPortType;

implementation

{ Layer1Type }

constructor Layer1Type.Create;
begin
  inherited Create;
  FPayload := TObjectList<Layer0Type>.Create(True);
end;

destructor Layer1Type.Destroy;
begin
  FPayload.Free;
  inherited Destroy;
end;

function GetFIS2ELAKPortType(UseWSDL: Boolean; Addr: string; HTTPRIO: THTTPRIO): FIS2ELAKPortType;
const
  defWSDL = 'http://example.org/ElakTrans_30.wsdl';
  defURL  = 'http://example.org/FIS2ELAK';
  defSvc  = 'FIS2ELAKService';
  defPrt  = 'FIS2ELAKPort';
begin
  Result := nil;
  if Addr = '' then
  begin
    if UseWSDL then
      Addr := defWSDL
    else
      Addr := defURL;
  end;
  if HTTPRIO = nil then
    HTTPRIO := THTTPRIO.Create(nil);
  Result := (HTTPRIO as FIS2ELAKPortType);
  if UseWSDL then
  begin
    HTTPRIO.WSDLLocation := Addr;
    HTTPRIO.Service := defSvc;
    HTTPRIO.Port := defPrt;
  end
  else
    HTTPRIO.URL := Addr;
end;


initialization
  InvRegistry.RegisterInterface(TypeInfo(FIS2ELAKPortType), 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'utf-8');
  RemClassRegistry.RegisterXSClass(SendDataInputObject, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'SendDataInputObject');
  RemClassRegistry.RegisterXSClass(CommonInputParameterType, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'CommonInputParameterType');
  RemClassRegistry.RegisterXSClass(RequestIdentifierType, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'RequestIdentifierType');
  RemClassRegistry.RegisterXSClass(EdiaktType, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'EdiaktType');
  RemClassRegistry.RegisterXSClass(HeaderType, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'HeaderType');
  RemClassRegistry.RegisterXSClass(MetaDataType, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'MetaDataType');
  RemClassRegistry.RegisterXSClass(IdentificationType, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'IdentificationType');
  RemClassRegistry.RegisterXSClass(PayloadType, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'PayloadType');
  RemClassRegistry.RegisterXSClass(Layer3Type, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'Layer3Type');
  RemClassRegistry.RegisterXSClass(Layer2Type, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'Layer2Type');
  RemClassRegistry.RegisterXSClass(Layer1Type, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'Layer1Type');
  RemClassRegistry.RegisterXSClass(Layer0Type, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'Layer0Type');
  RemClassRegistry.RegisterXSClass(PayloadType2, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'PayloadType2');
  RemClassRegistry.RegisterXSClass(BinaryDocumentType, 'http://reference.e-government.gv.at/namespace/elaktrans/3#', 'BinaryDocumentType');
end.
