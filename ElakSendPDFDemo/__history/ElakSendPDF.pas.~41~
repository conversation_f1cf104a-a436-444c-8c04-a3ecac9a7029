unit ElakSendPDF;

interface

uses
  System.SysUtils,
  System.Classes,
  System.Generics.Collections,
  System.NetEncoding,
  System.Zip,
  System.IOUtils,

  Soap.InvokeRegistry,
  Soap.Rio,
  Soap.SOAPHTTPClient,

  DX.Utils.Logger,

//  ElakTrans_SendData_TLB;
ELAK_WSDL;

type
  TSOAPLogger = class
    class procedure LogRequest(const MethodName: string; RequestStream: TStream);
    class procedure LogResponse(const MethodName: string; ResponseStream: TStream);
  end;

procedure SendPDFToElak(const APdfPath:string; ABKB: string);

implementation

function EncodeZipWithPDF(const APDFPath, ADocID: string): TArray<System.Byte>;
var
  LZipStream: TMemoryStream;
  LZip: TZipFile;
  LEncoded: TBytes;
begin
  LZipStream := TMemoryStream.Create;
  try
    LZip := TZipFile.Create;
    try
      LZip.Open(LZipStream, zmWrite);
      LZip.Add(APDFPath, ADocID + ExtractFileExt(APDFPath));
      LZip.Close;
    finally
      LZip.Free;
    end;
    LZipStream.Position := 0;
    SetLength(LEncoded, LZipStream.Size);
    LZipStream.ReadBuffer(LEncoded[0], LZipStream.Size);
    Result := LEncoded;
  finally
    LZipStream.Free;
  end;
end;

procedure SendPDFToElak(const APdfPath:string; ABKB: string);
var
  LHTTPRIO: THTTPRIO;
  LWS: ElakTrans;
  LInput: SendDataInputObject;
  LDocID: string;
  LLayer3: Layer3Type;
  LLayer2: Layer2Type;
  LLayer1: Layer1Type;
  LLayer0: Layer0Type;
  FLayer3Array: Array_Of_Layer3Type;
  FLayer2Array: Array_Of_Layer2Type;
  FLayer1Array: Array_Of_Layer1Type;
  FLayer0Array: Array_Of_Layer0Type;
begin
  LDocID := '';
  LHTTPRIO := nil;
  LInput := nil;
  LLayer0 := nil;
  LLayer1 := nil;
  LLayer2 := nil;
  LLayer3 := nil;

  LHTTPRIO := THTTPRIO.Create(nil);

  LHTTPRIO.OnBeforeExecute := TSOAPLogger.LogRequest;
  LHTTPRIO.OnAfterExecute := TSOAPLogger.LogResponse;

  LWS := GetElakTrans(False, '', LHTTPRIO);
  LInput := SendDataInputObject.Create;
  try
    // Initialize CommonInputParameter with required values
    LInput.CommonInputParameter := CommonInputParameterType.Create;
    with LInput.CommonInputParameter do
    begin
      SourceSystemId := 'ELKE-REST';  // Should be configured according to your system
      VKZType := 'VKZ123';  // Should be a valid VKZ (Verfahrenskennzahl)
      Procedure_ := 'ELAK.IMPORT';  // Standard procedure for importing documents
      
      // Set up request identifier with a new GUID and sequence number
      RequestIdentifier := RequestIdentifierType.Create;
      RequestIdentifier.ConversationIdentifier := TGUID.NewGuid.ToString;
      RequestIdentifier.SequenceIdentifier := '1';
    end;

    // Set the purpose of the request
    LInput.Purpose := PurposeType.INPUT;

    // Configure layer controls to specify what data we're sending
    LInput.LayerControl := LayerControlType.Create;
    
    // Layer 3 (Akt) control settings
    LInput.LayerControl.Layer3SendControl := LayerSendControlType.Create;
    LInput.LayerControl.Layer3SendControl.ContainsModifiedBasicData := True;
    
    // Layer 2 (Vorgang) control settings
    LInput.LayerControl.Layer2SendControl := LayerSendControlType.Create;
    LInput.LayerControl.Layer2SendControl.ContainsModifiedBasicData := True;
    
    // Layer 1 (Dokument) control settings
    LInput.LayerControl.Layer1SendControl := LayerSendControlType.Create;
    LInput.LayerControl.Layer1SendControl.ContainsModifiedBasicData := True;
    
    // Layer 0 (Binärdokument) control settings
    LInput.LayerControl.Layer0SendControl := LayerSendControlType.Create;
    LInput.LayerControl.Layer0SendControl.ContainsModifiedBasicData := True;
    LInput.LayerControl.Layer0SendControl.ContainsModifiedBinaryContent := True;

    LDocID := ABKB;
    LInput.Documents := EncodeZipWithPDF(APdfPath, LDocID);

    LInput.Ediakt := EdiaktType.Create;
    with LInput.Ediakt do
    begin
      Header := HeaderType.Create;
      MetaData := MetaDataType.Create;
      MetaData.Identifier := IdentificationType.Create;
      MetaData.ReferencedIdentifier := IdentificationType.Create;

      // Initialize Payload4 which contains an array of Layer3Type
      Payload := Payload4.Create;
      LLayer3 := Layer3Type.Create;
      // Create and set the array with one element
      SetLength(FLayer3Array, 1);
      FLayer3Array[0] := LLayer3;
      Payload.Layer3 := FLayer3Array;
      with LLayer3 do
      begin
        Subject := 'Kontrollakt zu ' + ABKB;
        MetaData := MetaDataType.Create;
        MetaData.Identifier := IdentificationType.Create;
        MetaData.ReferencedIdentifier := IdentificationType.Create;
        MetaData.ReferencedIdentifier.Identification := 'L3-' + ABKB;

        // Initialize Payload5 which contains an array of Layer2Type
        LLayer2 := Layer2Type.Create;
        Payload := Payload5.Create;
        // Create and set the array with one element
        SetLength(FLayer2Array, 1);
        FLayer2Array[0] := LLayer2;
        Payload.Layer2 := FLayer2Array;
        
        with LLayer2 do
        begin
          Subject := 'Kontrollfall';
          MetaData := MetaDataType.Create;
          MetaData.Identifier := IdentificationType.Create;
          MetaData.ReferencedIdentifier := IdentificationType.Create;
          MetaData.ReferencedIdentifier.Identification := 'L2-' + ABKB;

          // Initialize Payload2 which contains an array of Layer1Type
          LLayer1 := Layer1Type.Create;
          Payload := Payload2.Create;
          // Create and set the array with one element
          SetLength(FLayer1Array, 1);
          FLayer1Array[0] := LLayer1;
          Payload.Layer1 := FLayer1Array;
          
          with LLayer1 do
          begin
            Subject := 'Kontrollbericht';
            MetaData := MetaDataType.Create;
            MetaData.Identifier := IdentificationType.Create;
            MetaData.ReferencedIdentifier := IdentificationType.Create;
            MetaData.ReferencedIdentifier.Identification := 'L1-' + ABKB;
            MainDocument := LDocID;

            // Initialize Payload3 which contains an array of Layer0Type
            Payload := Payload3.Create;
            // Create and set the array with one element
            SetLength(FLayer0Array, 1);
            LLayer0 := Layer0Type.Create;
            FLayer0Array[0] := LLayer0;
            Payload.Layer0 := FLayer0Array;
            with LLayer0 do
            begin
              Id := LDocID;
              MetaData := MetaDataType.Create;
              MetaData.Identifier := IdentificationType.Create;
              MetaData.ReferencedIdentifier := IdentificationType.Create;
              MetaData.ReferencedIdentifier.Identification := ABKB;
              Payload := PayloadType2.Create;
              Payload.BinaryDocument := BinaryDocumentType.Create;
              Payload.BinaryDocument.EmbeddedFileURL := LDocID;
              Payload.BinaryDocument.FileName := ExtractFileName(APdfPath);
              Payload.BinaryDocument.MIMEType := 'application/pdf';
            end; // LLayer0 with
          end; // LLayer1 with
        end; // LLayer2 with
      end; // LLayer3 with
    end; // LInput.Ediakt with

    try
      LWS.SendData(LInput);
    except
      on E: Exception do
        raise Exception.Create('Fehler beim Senden an ELAK: ' + E.Message);
    end;
  finally
    if Assigned(LLayer1) then
      LLayer1.Payload.Free;
    FreeAndNil(LLayer0);
    FreeAndNil(LLayer1);
    FreeAndNil(LLayer2);
    FreeAndNil(LLayer3);
    FreeAndNil(LInput);
    FreeAndNil(LHTTPRIO);
  end;
end;

class procedure TSOAPLogger.LogRequest(const MethodName: string; RequestStream: TStream);
var
  LStrings: TStringList;
begin
  LStrings := TStringList.Create;
  try
    RequestStream.Position := 0;
    LStrings.LoadFromStream(RequestStream, TEncoding.UTF8);
    DXLog(LStrings.Text);
  finally
    LStrings.Free;
  end;
end;

class procedure TSOAPLogger.LogResponse(const MethodName: string; ResponseStream: TStream);
var
  LStrings: TStringList;
begin
  LStrings := TStringList.Create;
  try
    ResponseStream.Position := 0;
    LStrings.LoadFromStream(ResponseStream, TEncoding.UTF8);
    DXLog(LStrings.Text);
  finally
    LStrings.Free;
  end;
end;

end.

