program TestDXLogOverloads;

{$APPTYPE CONSOLE}

{$R *.res}

uses
  System.SysUtils,
  DX.Utils.Logger in 'dx-library\DX.Utils.Logger.pas',
  DX.Utils.Logger.Intf in 'dx-library\DX.Utils.Logger.Intf.pas';

begin
  try
    WriteLn('DXLog Overloads Test');
    WriteLn('===================');
    WriteLn;
    
    // Bestehende Overloads (ohne LogLevel)
    DXLog('Dies ist eine einfache Log-Nachricht');
    DXLog('Dies ist eine formatierte Nachricht: %s = %d', ['Wert', 42]);
    
    // Neue Overloads (mit LogLevel)
    DXLog('Dies ist eine Debug-Nachricht', TLogLevel.Debug);
    DXLog('Dies ist eine Info-Nachricht', TLogLevel.Info);
    DXLog('Dies ist eine Warning-Nachricht', TLogLevel.Warning);
    DXLog('Dies ist eine Error-Nachricht', TLogLevel.Error);
    
    // Neue formatierte Overloads (mit LogLevel)
    DXLog('Debug: Benutzer %s hat %d Versuche', ['Max Mustermann', 3], TLogLevel.Debug);
    DXLog('Info: Verarbeitung von %s abgeschlossen in %d ms', ['Datei.pdf', 1250], TLogLevel.Info);
    DXLog('Warning: Speicher bei %d%% Auslastung', [85], TLogLevel.Warning);
    DXLog('Error: Verbindung zu %s fehlgeschlagen (Code: %d)', ['Server', 404], TLogLevel.Error);
    
    WriteLn;
    WriteLn('Alle DXLog Overloads getestet!');
    WriteLn('Prüfen Sie die Log-Datei für die Ausgabe.');
    WriteLn('Drücken Sie Enter zum Beenden...');
    ReadLn;
    
  except
    on E: Exception do
    begin
      WriteLn('Fehler: ', E.ClassName, ': ', E.Message);
      WriteLn('Drücken Sie Enter zum Beenden...');
      ReadLn;
    end;
  end;
end.
