# ELAK Metadaten - Arbeitsanleitung für beteiligte Parteien

> **Zweck:** Anleitung zur Verwendung der ELAK Metadaten-Dokumentation  
> **Zielgruppe:** Fachbereich, IT-Entwicklung, Projektleitung  
> **Version:** 1.0  

---

## Übersicht der Dokumente

### 📋 Hauptdokumentation
- **`ELAK_Metadaten_Dokumentation.md`** - Vollständige technische Dokumentation aller verfügbaren Metadatenfelder

### 📊 Konfigurationstabelle  
- **`ELAK_Metadaten_Konfiguration.csv`** - Bearbeitbare Tabelle zur Festlegung der Metadaten-Befüllung

### 📖 Diese Anleitung
- **`README_Metadaten_Arbeitsanleitung.md`** - Arbeitsanleitung für alle Beteiligten

---

## Arbeitsschritte

### Schritt 1: Dokumentation studieren 📖

**Wer:** Alle Beteiligten  
**Was:** Lesen Sie die `ELAK_Metadaten_Dokumentation.md`

**Fokus je nach Rolle:**
- **Fachbereich:** Abschnitte "Layer 1", "Layer 0", "MetaDataType"
- **IT-Entwicklung:** Alle Abschnitte, besonders "Implementierungshinweise"
- **Projektleitung:** "Übersicht", "Konfigurationstabelle", "Nächste Schritte"

### Schritt 2: CSV-Datei bearbeiten 📊

**Wer:** Fachbereich + IT-Entwicklung gemeinsam  
**Was:** Öffnen Sie `ELAK_Metadaten_Konfiguration.csv` in Excel/LibreOffice

**Spalten bearbeiten:**
- **Gewünschter Wert:** Tragen Sie die gewünschten Werte ein
- **Datenquelle:** Bestätigen/korrigieren Sie die Datenquelle
- **Priorität:** Anpassen falls erforderlich
- **Bemerkungen:** Ergänzen Sie fachliche Hinweise

### Schritt 3: Prioritäten festlegen 🎯

**Kategorien:**
- **🔴 Pflicht:** Muss implementiert werden
- **🟡 Hoch:** Sollte in Phase 1 implementiert werden  
- **🟢 Mittel:** Kann in Phase 2 implementiert werden
- **⚪ Niedrig:** Optional für spätere Phasen

**Empfohlenes Vorgehen:**
1. Alle Pflichtfelder bestätigen
2. "Hoch"-Priorität Felder für Phase 1 festlegen
3. "Mittel"-Priorität Felder für Phase 2 planen
4. "Niedrig"-Priorität Felder als optional markieren

### Schritt 4: Datenquellen klären 💾

**Für jedes Feld prüfen:**
- Ist die Datenquelle verfügbar?
- Welches System liefert die Daten?
- Sind Anpassungen erforderlich?
- Gibt es Standardwerte?

**Datenquellen-Kategorien:**
- **Parameter:** Aus Funktionsaufruf (z.B. BKB-Nummer)
- **Kontrollsystem:** Aus dem Kontrollsystem
- **Benutzersystem:** Aus Benutzer-/Rechteverwaltung
- **Konfiguration:** Aus INI-Datei oder Konfiguration
- **Code:** Fest im Code definiert
- **System:** Automatisch generiert (Datum/Zeit)
- **Berechnung:** Berechnet aus anderen Werten

### Schritt 5: Implementierungsplan erstellen 📅

**Phase 1 - Grundimplementierung:**
- Alle Pflichtfelder
- Felder mit Priorität "Hoch"
- Basis-Konfiguration über INI-Datei

**Phase 2 - Erweiterte Metadaten:**
- Felder mit Priorität "Mittel"
- Erweiterte Konfigurationsmöglichkeiten
- Integration mit Kontrollsystem

**Phase 3 - Vollständige Integration:**
- Felder mit Priorität "Niedrig"
- Komplexe Datentypen (PersonDataType, etc.)
- Workflow-Integration

---

## Rollen und Verantwortlichkeiten

### 👥 Fachbereich
**Verantwortlich für:**
- Fachliche Anforderungen definieren
- Prioritäten festlegen
- Datenquellen identifizieren
- Standardwerte bestimmen
- Fachliche Validierung

**Aufgaben:**
- CSV-Datei: Spalte "Gewünschter Wert" befüllen
- CSV-Datei: Spalte "Bemerkungen" ergänzen
- Prioritäten überprüfen und anpassen
- Fachliche Review der Implementierung

### 💻 IT-Entwicklung
**Verantwortlich für:**
- Technische Umsetzbarkeit bewerten
- Implementierungsaufwand schätzen
- Datenquellen-Integration planen
- Code-Implementierung

**Aufgaben:**
- CSV-Datei: Spalte "Datenquelle" validieren
- Technische Machbarkeit prüfen
- Implementierungsplan erstellen
- Code-Entwicklung und Tests

### 📋 Projektleitung
**Verantwortlich für:**
- Koordination zwischen Fachbereich und IT
- Zeitplanung und Ressourcen
- Entscheidungen bei Konflikten
- Qualitätssicherung

**Aufgaben:**
- Meetings koordinieren
- Entscheidungen dokumentieren
- Fortschritt überwachen
- Abnahme organisieren

---

## Arbeitsvorlagen

### 📝 Meeting-Agenda Template

```
ELAK Metadaten Review Meeting
Datum: [DATUM]
Teilnehmer: [NAMEN]

Agenda:
1. Review der CSV-Konfiguration
2. Klärung offener Fragen
3. Prioritäten finalisieren
4. Nächste Schritte festlegen

Offene Punkte:
- [ ] Feld XYZ: Datenquelle klären
- [ ] Priorität von Feld ABC bestimmen
- [ ] Standardwert für Feld DEF festlegen

Entscheidungen:
- [ENTSCHEIDUNG 1]
- [ENTSCHEIDUNG 2]

Nächste Schritte:
- [ ] [AUFGABE] - [VERANTWORTLICH] - [TERMIN]
```

### ✅ Checkliste für Feldkonfiguration

Für jedes Metadatenfeld prüfen:

- [ ] **Fachliche Relevanz:** Wird das Feld benötigt?
- [ ] **Datenquelle:** Woher kommen die Daten?
- [ ] **Verfügbarkeit:** Sind die Daten verfügbar?
- [ ] **Standardwert:** Gibt es einen Standardwert?
- [ ] **Validierung:** Welche Validierungsregeln gelten?
- [ ] **Priorität:** Wann soll implementiert werden?
- [ ] **Aufwand:** Wie hoch ist der Implementierungsaufwand?

### 📊 Status-Tracking Template

| Feld | Status | Verantwortlich | Termin | Bemerkung |
|------|--------|----------------|--------|-----------|
| Layer1.Subject | ✅ Fertig | IT | - | Implementiert |
| Layer1.MetaData.Date | 🟡 In Arbeit | IT | 2025-02-01 | Datenquelle klären |
| Layer1.MetaData.CreatedBy | ❌ Offen | Fachbereich | 2025-02-15 | Anforderung definieren |

**Legende:**
- ✅ Fertig
- 🟡 In Arbeit  
- ❌ Offen
- ⏸️ Blockiert

---

## Häufige Fragen (FAQ)

### ❓ Welche Felder sind wirklich Pflicht?
**Antwort:** Nur die mit Status "🔴 Pflicht" markierten Felder sind technisch erforderlich. Alle anderen sind optional, aber fachlich möglicherweise wichtig.

### ❓ Können wir später weitere Felder hinzufügen?
**Antwort:** Ja, die ELAK-Schnittstelle ist erweiterbar. Neue Felder können in späteren Phasen ergänzt werden.

### ❓ Was passiert wenn ein Feld nicht befüllt wird?
**Antwort:** Optionale Felder werden einfach weggelassen. Pflichtfelder führen zu einem Fehler.

### ❓ Wie werden Arrays/Listen befüllt?
**Antwort:** Arrays werden als kommagetrennte Werte konfiguriert und im Code entsprechend aufgeteilt.

### ❓ Können Standardwerte überschrieben werden?
**Antwort:** Ja, über die INI-Konfiguration oder zur Laufzeit im Code.

---

## Kontakt und Support

**Bei Fragen zur Dokumentation:**
- Technische Fragen → IT-Entwicklung
- Fachliche Fragen → Fachbereich  
- Koordination → Projektleitung

**Dokumentation aktualisieren:**
- Änderungen in CSV-Datei dokumentieren
- Hauptdokumentation entsprechend anpassen
- Alle Beteiligten informieren

---

**Letzte Aktualisierung:** 2025-01-27  
**Nächste Review:** [DATUM EINTRAGEN]
