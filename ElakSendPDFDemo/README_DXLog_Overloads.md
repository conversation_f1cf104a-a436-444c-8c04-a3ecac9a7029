# DXLog Overloads mit LogLevel

## Übersicht

Die `DX.Utils.Logger.pas` wurde um zwei zusätzliche Overloads für `DXLog()` erwei<PERSON>t, die einen optionalen `LogLevel` Parameter unterstützen.

## Neue Overloads

### 1. DXLog mit einfacher Nachricht und LogLevel

```delphi
procedure DXLog(const AMessage: string; const ALogLevel: TLogLevel); overload;
```

**Verwendung:**
```delphi
DXLog('Dies ist eine Debug-Nachricht', TLogLevel.Debug);
DXLog('Dies ist eine Info-Nachricht', TLogLevel.Info);
DXLog('Dies ist eine Warning-Nachricht', TLogLevel.Warning);
DXLog('Dies ist eine Error-Nachricht', TLogLevel.Error);
```

### 2. DXLog mit formatierter Nachricht und LogLevel

```delphi
procedure DXLog(
  const AFormatString: string;
  const AValues: array of const;
  const ALogLevel: TLogLevel); overload;
```

**Verwendung:**
```delphi
DXLog('Debug: Benutzer %s hat %d Versuche', ['Max Mustermann', 3], TLogLevel.Debug);
DXLog('Info: Verarbeitung von %s abgeschlossen in %d ms', ['Datei.pdf', 1250], TLogLevel.Info);
DXLog('Warning: Speicher bei %d%% Auslastung', [85], TLogLevel.Warning);
DXLog('Error: Verbindung zu %s fehlgeschlagen (Code: %d)', ['Server', 404], TLogLevel.Error);
```

## Verfügbare LogLevel

```delphi
TLogLevel = (Debug, Info, Warning, Error);
```

- **Debug**: Detaillierte Informationen für Debugging-Zwecke
- **Info**: Allgemeine Informationsmeldungen
- **Warning**: Warnungen, die Aufmerksamkeit erfordern
- **Error**: Fehlermeldungen

## Bestehende Overloads (unverändert)

Die bestehenden Overloads funktionieren weiterhin wie gewohnt:

```delphi
// Einfache Nachricht (Standard LogLevel: Info)
DXLog('Einfache Nachricht');

// Formatierte Nachricht (Standard LogLevel: Info)
DXLog('Formatierte Nachricht: %s = %d', ['Wert', 42]);
```

## Implementierungsdetails

Die neuen Overloads rufen direkt die entsprechenden Methoden der `TDXLogger` Klasse auf:

```delphi
procedure DXLog(const AMessage: string; const ALogLevel: TLogLevel); overload;
begin
  TDXLogger.Instance.Log(AMessage, ALogLevel);
end;

procedure DXLog(
  const AFormatString: string;
  const AValues: array of const;
  const ALogLevel: TLogLevel); overload;
begin
  TDXLogger.Instance.Log(AFormatString, AValues, ALogLevel);
end;
```

## Verwendung im ELAK Projekt

### ElakConfig.pas
```delphi
DXLog('ElakConfig.ini nicht gefunden. Verwende Standardwerte.', TLogLevel.Warning);
DXLog('ElakConfig.ini gefunden: ' + FConfigPath, TLogLevel.Info);
```

### ElakSendPDF.pas
```delphi
DXLog('ELAK Service URL: ' + LServiceURL, TLogLevel.Info);
DXLog('SOAP Request (%s): %s', [MethodName, LStrings.Text], TLogLevel.Debug);
DXLog('SOAP Response (%s): %s', [MethodName, LStrings.Text], TLogLevel.Debug);
```

## Vorteile

1. **Bessere Kategorisierung**: Log-Nachrichten können nach Wichtigkeit kategorisiert werden
2. **Flexibles Filtering**: Möglichkeit, nur bestimmte LogLevel anzuzeigen
3. **Abwärtskompatibilität**: Bestehender Code funktioniert unverändert
4. **Konsistenz**: Einheitliche API für alle Logging-Szenarien

## Migration

Bestehender Code muss nicht geändert werden. Die neuen Overloads sind optional und ergänzen die bestehende Funktionalität.

### Empfohlene Migration:
```delphi
// Vorher
DXLog('Fehler beim Laden der Datei');

// Nachher (empfohlen)
DXLog('Fehler beim Laden der Datei', TLogLevel.Error);
```

## Test

Verwenden Sie `TestDXLogOverloads.pas` um alle Overloads zu testen:

```bash
dcc32 TestDXLogOverloads.pas
TestDXLogOverloads.exe
```
