# ELAK SOAP Speicherverwaltung

## Problem

Bei der Verwendung der ELAK SOAP-Schnittstelle trat eine **AccessViolation** beim <PERSON>ei<PERSON>ben der Layer-Objekte auf:

```delphi
FreeAndNil(LLayer1); // AccessViolation!
```

## Ursache

Die SOAP-Framework-generierten Klassen (`Layer1Type`, `Layer2Type`, etc.) haben automatische Speicherverwaltung implementiert. Die Destruktoren geben automatisch alle enthaltenen Objekte frei:

### Layer1Type Destruktor (aus ELAK_WSDL.pas):
```delphi
destructor Layer1Type.Destroy;
var
  I: Integer;
begin
  for I := 0 to System.Length(FProcessData)-1 do
    System.SysUtils.FreeAndNil(FProcessData[I]);
  System.SetLength(FProcessData, 0);
  for I := 0 to System.Length(FPayload)-1 do
    System.SysUtils.FreeAndNil(FPayload[I]);  // <-- Hier werden Layer0 Objekte automatisch freigegeben
  System.SetLength(FPayload, 0);
  // ... weitere Freigaben
  inherited Destroy;
end;
```

## Objekthierarchie

```
SendDataInputObject (LInput)
├── EdiaktType
│   └── Payload4
│       └── Layer3Type (LLayer3)
│           └── Payload5
│               └── Layer2Type (LLayer2)
│                   └── Payload2
│                       └── Layer1Type (LLayer1)
│                           └── Payload3 (Array)
│                               └── Layer0Type (LLayer0)
```

## Speicherverwaltung

### ❌ Falsch (führt zu AccessViolation):
```delphi
finally
  FreeAndNil(LLayer0);  // AccessViolation - bereits durch LLayer1 freigegeben
  FreeAndNil(LLayer1);  // AccessViolation - bereits durch LLayer2 freigegeben
  FreeAndNil(LLayer2);  // AccessViolation - bereits durch LLayer3 freigegeben
  FreeAndNil(LLayer3);  // AccessViolation - bereits durch LInput freigegeben
  FreeAndNil(LInput);
  FreeAndNil(LHTTPRIO);
end;
```

### ✅ Korrekt (automatische Kaskadierung):
```delphi
finally
  // Layer objects (LLayer0, LLayer1, LLayer2, LLayer3) are automatically freed
  // by their parent objects' destructors when LInput is freed.
  // Manual freeing would cause AccessViolation due to double-free.
  FreeAndNil(LInput);  // This will cascade-free all Layer objects
  FreeAndNil(LHTTPRIO);
end;
```

## Warum passiert das?

1. **Ownership-Kette**: Jedes übergeordnete Objekt übernimmt die Ownership der untergeordneten Objekte
2. **Array-Verwaltung**: Layer-Objekte werden in Arrays gespeichert, die automatisch verwaltet werden
3. **SOAP-Framework**: Die generierten Klassen implementieren automatische Cleanup-Mechanismen
4. **Kaskadierung**: `LInput.Free` → `EdiaktType.Free` → `Layer3Type.Free` → ... → `Layer0Type.Free`

## Debugging-Tipps

### Symptome einer AccessViolation:
- Fehler beim `FreeAndNil()` Aufruf
- "Access violation at address..." Meldungen
- Unvorhersagbare Crashes beim Cleanup

### Debugging-Schritte:
1. **Prüfen Sie die Objekthierarchie**: Welche Objekte sind in anderen enthalten?
2. **Destruktor analysieren**: Schauen Sie in die WSDL-generierten Destruktoren
3. **Ownership verfolgen**: Wer ist für die Freigabe verantwortlich?
4. **Schrittweise testen**: Kommentieren Sie FreeAndNil-Aufrufe aus

## Best Practices

### 1. Nur Top-Level-Objekte freigeben
```delphi
// Nur das Haupt-Input-Objekt freigeben
FreeAndNil(LInput);  // Gibt automatisch alle Layer frei
```

### 2. Dokumentation der Ownership
```delphi
// Layer objects will be automatically managed by SOAP framework
LLayer0 := Layer0Type.Create;  // Wird automatisch durch LLayer1 verwaltet
```

### 3. Defensive Programmierung
```delphi
try
  // SOAP Aufruf
except
  on E: Exception do
  begin
    DXLog('SOAP Fehler: ' + E.Message, TLogLevel.Error);
    raise;
  end;
end;
```

## Weitere SOAP-Objekte

Diese Regel gilt für alle SOAP-generierten Objekte:
- `SendDataInputObject`
- `CommonInputParameterType`
- `LayerControlType`
- `EdiaktType`
- `MetaDataType`
- `IdentificationType`
- Alle `Layer*Type` Klassen
- Alle `Payload*` Klassen

## Fazit

**Regel**: Bei SOAP-generierten Objekten nur die Top-Level-Objekte manuell freigeben. Alle untergeordneten Objekte werden automatisch durch die Destruktor-Kaskadierung verwaltet.

Dies verhindert AccessViolations und sorgt für saubere Speicherverwaltung.
