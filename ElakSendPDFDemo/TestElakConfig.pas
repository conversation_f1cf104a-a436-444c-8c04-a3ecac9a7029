program TestElakConfig;

{$APPTYPE CONSOLE}

{$R *.res}

uses
  System.SysUtils,
  ElakConfig in 'ElakConfig.pas',
  DX.Utils.Logger in 'dx-library\DX.Utils.Logger.pas';

var
  LConfig: TElakConfig;
begin
  try
    WriteLn('ELAK Konfiguration Test');
    WriteLn('=======================');
    WriteLn;
    
    // Konfiguration laden
    LConfig := TElakConfig.Instance;
    
    // Konfigurationswerte anzeigen
    WriteLn('Service URL: ', LConfig.GetServiceURL);
    WriteLn('WSDL URL: ', LConfig.GetWSDLURL);
    WriteLn('Source System ID: ', LConfig.GetSourceSystemId);
    WriteLn('Default Procedure: ', LConfig.GetDefaultProcedure);
    WriteLn('SOAP Logging: ', BoolToStr(LConfig.IsSOAPLoggingEnabled, True));
    
    WriteLn;
    WriteLn('Konfiguration erfolgreich geladen!');
    WriteLn('Drücken Sie Enter zum Beenden...');
    ReadLn;
    
  except
    on E: Exception do
    begin
      WriteLn('Fehler: ', E.ClassName, ': ', E.Message);
      WriteLn('Drücken Sie Enter zum Beenden...');
      ReadLn;
    end;
  end;
end.
