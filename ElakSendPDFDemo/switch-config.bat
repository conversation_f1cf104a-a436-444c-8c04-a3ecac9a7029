@echo off
echo ELAK Konfiguration wechseln
echo ===========================
echo.
echo 1. Produktionsumgebung (ProductionVM)
echo 2. Testumgebung (TestVM) 
echo 3. Entwicklungsumgebung (localhost)
echo 4. Benutzerdefiniert
echo.
set /p choice="Wählen Sie eine Option (1-4): "

if "%choice%"=="1" goto production
if "%choice%"=="2" goto test
if "%choice%"=="3" goto development
if "%choice%"=="4" goto custom
goto end

:production
echo Wechsle zu Produktionsumgebung...
(
echo [ELAK]
echo ServiceURL=http://ProductionVM:8080/ELAK
echo WSDLURL=http://ProductionVM:8080/ELAK?WSDL
echo.
echo [System]
echo SourceSystemId=ELKE-REST
echo DefaultProcedure=ELAK.IMPORT
echo.
echo [Logging]
echo EnableSOAPLogging=false
) > ElakConfig.ini
echo Konfiguration für Produktionsumgebung erstellt.
goto end

:test
echo Wechsle zu Testumgebung...
(
echo [ELAK]
echo ServiceURL=http://TestVM:8080/ELAK
echo WSDLURL=http://TestVM:8080/ELAK?WSDL
echo.
echo [System]
echo SourceSystemId=ELKE-REST
echo DefaultProcedure=ELAK.IMPORT
echo.
echo [Logging]
echo EnableSOAPLogging=true
) > ElakConfig.ini
echo Konfiguration für Testumgebung erstellt.
goto end

:development
echo Wechsle zu Entwicklungsumgebung...
(
echo [ELAK]
echo ServiceURL=http://localhost:8080/ELAK
echo WSDLURL=http://localhost:8080/ELAK?WSDL
echo.
echo [System]
echo SourceSystemId=ELKE-REST
echo DefaultProcedure=ELAK.IMPORT
echo.
echo [Logging]
echo EnableSOAPLogging=true
) > ElakConfig.ini
echo Konfiguration für Entwicklungsumgebung erstellt.
goto end

:custom
set /p url="Geben Sie die ELAK Service URL ein: "
(
echo [ELAK]
echo ServiceURL=%url%
echo WSDLURL=%url%?WSDL
echo.
echo [System]
echo SourceSystemId=ELKE-REST
echo DefaultProcedure=ELAK.IMPORT
echo.
echo [Logging]
echo EnableSOAPLogging=true
) > ElakConfig.ini
echo Benutzerdefinierte Konfiguration erstellt.
goto end

:end
echo.
echo Drücken Sie eine beliebige Taste zum Beenden...
pause >nul
