﻿unit ElakConfig;

interface

uses
  System.SysUtils, System.Classes, System.IniFiles, System.IOUtils;

type
  /// <summary>
  /// Konfigurationsklasse für ELAK Service-Einstellungen
  /// Liest Konfiguration aus ElakConfig.ini Datei
  /// </summary>
  TElakConfig = class
  private
    class var FInstance: TElakConfig;
    class function GetInstance: TElakConfig; static;
  private
    FIniFile: TIniFile;
    FConfigPath: string;
    procedure InitializeConfig;
  public
    constructor Create;
    destructor Destroy; override;

    /// <summary>
    /// Singleton-Instanz der Konfiguration
    /// </summary>
    class property Instance: TElakConfig read GetInstance;

    /// <summary>
    /// URL des ELAK SOAP Services
    /// </summary>
    function GetServiceURL: string;

    /// <summary>
    /// WSDL URL des ELAK Services
    /// </summary>
    function GetWSDLURL: string;

    /// <summary>
    /// Source System ID für ELAK Requests
    /// </summary>
    function GetSourceSystemId: string;

    /// <summary>
    /// Standard-Prozedur für ELAK Import
    /// </summary>
    function GetDefaultProcedure: string;

    /// <summary>
    /// Gibt an, ob SOAP Logging aktiviert ist
    /// </summary>
    function IsSOAPLoggingEnabled: Boolean;

    /// <summary>
    /// Lädt die Konfiguration neu
    /// </summary>
    procedure ReloadConfig;
  end;

implementation

uses
  DX.Utils.Logger;

{ TElakConfig }

constructor TElakConfig.Create;
begin
  inherited;
  InitializeConfig;
end;

destructor TElakConfig.Destroy;
begin
  FreeAndNil(FIniFile);
  inherited;
end;

class function TElakConfig.GetInstance: TElakConfig;
begin
  if FInstance = nil then
    FInstance := TElakConfig.Create;
  Result := FInstance;
end;

procedure TElakConfig.InitializeConfig;
begin
  // Konfigurationsdatei im gleichen Verzeichnis wie die Anwendung suchen
  FConfigPath := TPath.Combine(TPath.GetDirectoryName(ParamStr(0)), 'ElakConfig.ini');

  // Falls nicht gefunden, im aktuellen Verzeichnis suchen
  if not TFile.Exists(FConfigPath) then
    FConfigPath := TPath.Combine(GetCurrentDir, 'ElakConfig.ini');

  // Falls immer noch nicht gefunden, Standardpfad verwenden
  if not TFile.Exists(FConfigPath) then
  begin
    FConfigPath := 'ElakConfig.ini';
    DXLog('ElakConfig.ini nicht gefunden. Verwende Standardwerte.', TLogLevel.Warn);
  end
  else
  begin
    DXLog('ElakConfig.ini gefunden: ' + FConfigPath, TLogLevel.Info);
  end;

  FIniFile := TIniFile.Create(FConfigPath);
end;

function TElakConfig.GetServiceURL: string;
begin
  Result := FIniFile.ReadString('ELAK', 'ServiceURL', 'http://ProductionVM:8080/ELAK');
end;

function TElakConfig.GetWSDLURL: string;
begin
  Result := FIniFile.ReadString('ELAK', 'WSDLURL', 'http://ProductionVM:8080/ELAK?WSDL');
end;

function TElakConfig.GetSourceSystemId: string;
begin
  Result := FIniFile.ReadString('System', 'SourceSystemId', 'ELKE');
end;

function TElakConfig.GetDefaultProcedure: string;
begin
  Result := FIniFile.ReadString('System', 'DefaultProcedure', 'ELAK.IMPORT');
end;

function TElakConfig.IsSOAPLoggingEnabled: Boolean;
begin
  Result := FIniFile.ReadBool('Logging', 'EnableSOAPLogging', True);
end;

procedure TElakConfig.ReloadConfig;
begin
  FreeAndNil(FIniFile);
  InitializeConfig;
end;

initialization

finalization
  FreeAndNil(TElakConfig.FInstance);

end.
