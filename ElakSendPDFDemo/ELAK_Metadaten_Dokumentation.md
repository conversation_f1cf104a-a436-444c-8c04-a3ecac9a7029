# ELAK Metadaten-Dokumentation für Layer 0 und Layer 1

> **Version:** 1.0  
> **Datum:** 2025-01-27  
> **Zweck:** Arbeitsgrundlage für alle beteiligten Parteien zur Bestimmung der ELAK-Metadaten-Befüllung

---

## Inhaltsverzeichnis

- [Übersicht](#übersicht)
- [Layer 1 (Dokument-Ebene)](#layer-1-dokument-ebene)
- [Layer 0 (Binärdokument-Ebene)](#layer-0-binärdokument-ebene)
- [MetaDataType (Gemeinsame Metadaten)](#metadatatype-gemeinsame-metadaten)
- [IdentificationType (Identifikation)](#identificationtype-identifikation)
- [PDF-Dokument Metadaten](#pdf-dokument-metadaten)
- [Konfigurationstabelle](#konfigurationstabelle)

---

## Übersicht

Diese Dokumentation beschreibt alle verfügbaren Metadatenfelder für Layer 1 und Layer 0 in der ELAK-Integration. Die Felder sind nach **Pflichtfeldern** (🔴), **Empfohlenen Feldern** (🟡) und **Optionalen Feldern** (🟢) kategorisiert.

### Aktuelle Implementierung
- **Layer 3**: ❌ Nicht verwendet
- **Layer 2**: ❌ Nicht verwendet  
- **Layer 1**: ✅ Kontrollbericht-Ebene
- **Layer 0**: ✅ PDF-Dokument-Ebene

---

## Layer 1 (Dokument-Ebene)

### Grundlegende Eigenschaften

| Feld | Typ | Status | Aktueller Wert | Beschreibung |
|------|-----|--------|----------------|--------------|
| **Subject** | `string` | 🔴 Pflicht | `'Kontrollbericht'` | Betreff des Dokuments |
| **Title** | `string` | 🟢 Optional | - | Zusätzlicher Titel |
| **MainDocument** | `string` | 🟡 Empfohlen | `ABKB` | Verweis auf Haupt-PDF |
| **BusinessType** | `string` | 🟢 Optional | - | Geschäftstyp/Verfahrensart |

### Layer 1 MetaData

| Feld | Typ | Status | Aktueller Wert | Beschreibung |
|------|-----|--------|----------------|--------------|
| **Identifier.Identification** | `string` | 🔴 Pflicht | `ABKB` | Eindeutige Identifikation |
| **ReferencedIdentifier.Identification** | `string` | 🔴 Pflicht | `ABKB` | Referenz-Identifikation |

### Erweiterte Layer 1 Felder

| Feld | Typ | Status | Beschreibung | Verwendung |
|------|-----|--------|--------------|------------|
| **ProcessData** | `Array` | 🟢 Optional | Prozessdaten/Workflow | Workflow-Integration |
| **References** | `ReferencesType` | 🟢 Optional | Dokumentenreferenzen | Verknüpfungen |
| **SpecialData** | `SpecialDataType` | 🟢 Optional | Spezielle XML-Daten | Fachspezifische Daten |
| **KeyValuePairs** | `KeyValuePairsType` | 🟢 Optional | Schlüssel-Wert-Paare | Zusätzliche Attribute |
| **AccountingData** | `AccountingDataType` | 🟢 Optional | Abrechnungsdaten | Kostenrechnung |
| **ElakSignature** | `Array` | 🟢 Optional | ELAK-Signaturen | Digitale Signaturen |

---

## Layer 0 (Binärdokument-Ebene)

### Grundlegende Eigenschaften

| Feld | Typ | Status | Aktueller Wert | Beschreibung |
|------|-----|--------|----------------|--------------|
| **id** | `string` | 🟡 Empfohlen | `ABKB` | Eindeutige Dokument-ID |
| **Subject** | `string` | 🟢 Optional | - | Betreff des Binärdokuments |
| **Title** | `string` | 🟢 Optional | - | Titel des Binärdokuments |

### Layer 0 MetaData

| Feld | Typ | Status | Aktueller Wert | Beschreibung |
|------|-----|--------|----------------|--------------|
| **Identifier.Identification** | `string` | 🔴 Pflicht | `ABKB` | Eindeutige Identifikation |
| **ReferencedIdentifier.Identification** | `string` | 🔴 Pflicht | `ABKB` | Referenz-Identifikation |

### PDF-Dokument (Payload.BinaryDocument)

| Feld | Typ | Status | Aktueller Wert | Beschreibung |
|------|-----|--------|----------------|--------------|
| **EmbeddedFileURL** | `string` | 🔴 Pflicht | `ABKB` | URL/ID der eingebetteten Datei |
| **FileName** | `string` | 🔴 Pflicht | `ExtractFileName(APdfPath)` | Dateiname der PDF |
| **MIMEType** | `string` | 🔴 Pflicht | `'application/pdf'` | MIME-Type |

### Erweiterte Layer 0 Felder

| Feld | Typ | Status | Beschreibung | Verwendung |
|------|-----|--------|--------------|------------|
| **ProcessData** | `Array` | 🟢 Optional | Prozessdaten | Workflow-Integration |
| **Representation** | `Array` | 🟢 Optional | Alternative Darstellungen | Verschiedene Formate |
| **SpecialData** | `SpecialDataType` | 🟢 Optional | Spezielle XML-Daten | Fachspezifische Daten |
| **KeyValuePairs** | `KeyValuePairsType` | 🟢 Optional | Schlüssel-Wert-Paare | Zusätzliche Attribute |
| **ElakSignature** | `Array` | 🟢 Optional | ELAK-Signaturen | Digitale Signaturen |

---

## MetaDataType (Gemeinsame Metadaten)

### Identifikation und Referenzen

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **Identifier** | `IdentificationType` | 🔴 Pflicht | Hauptidentifikation | siehe IdentificationType |
| **ReferencedIdentifier** | `IdentificationType` | 🟡 Empfohlen | Referenz-Identifikation | siehe IdentificationType |
| **ParentSubject** | `string` | 🟢 Optional | Übergeordneter Betreff | `'Lebensmittelkontrolle'` |
| **ParentIdentifier** | `IdentificationType` | 🟢 Optional | Übergeordnete ID | - |

### Datum und Zeit

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **Date** | `DateTime` | 🟡 Empfohlen | Erstellungsdatum | `2025-01-27T10:30:00Z` |
| **ReceivingDate** | `DateTime` | 🟢 Optional | Eingangsdatum | `2025-01-27T09:00:00Z` |
| **SendingDate** | `DateTime` | 🟢 Optional | Versendedatum | `2025-01-27T11:00:00Z` |
| **DeliveryDate** | `DateTime` | 🟢 Optional | Zustelldatum | `2025-01-27T12:00:00Z` |
| **LastChange** | `DateTime` | 🟢 Optional | Letzte Änderung | `2025-01-27T10:45:00Z` |
| **ExternalDate** | `DateTime` | 🟢 Optional | Externes Datum | - |
| **PostmarkDate** | `DateTime` | 🟢 Optional | Poststempel-Datum | - |

### Personen und Organisation

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **Editor** | `PersonDataType` | 🟢 Optional | Bearbeiter | siehe PersonDataType |
| **CreatedBy** | `PersonDataType` | 🟢 Optional | Ersteller | siehe PersonDataType |
| **LastChangeBy** | `PersonDataType` | 🟢 Optional | Letzter Bearbeiter | siehe PersonDataType |
| **Sender** | `PersonDataType` | 🟢 Optional | Absender | siehe PersonDataType |
| **ApprovedBy** | `PersonDataType` | 🟢 Optional | Genehmiger | siehe PersonDataType |
| **OrganisationalUnit** | `string` | 🟢 Optional | Organisationseinheit | `'Lebensmittelaufsicht'` |

### Status und Workflow

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **Priority** | `Boolean` | 🟢 Optional | Priorität | `false` |
| **Closed** | `Boolean` | 🟢 Optional | Geschlossen | `false` |
| **Canceled** | `Boolean` | 🟢 Optional | Storniert | `false` |
| **Approved** | `Boolean` | 🟢 Optional | Genehmigt | `true` |
| **Suspended** | `Boolean` | 🟢 Optional | Ausgesetzt | `false` |
| **SubmissionExcepted** | `Boolean` | 🟢 Optional | Vorlage ausgenommen | `false` |

### Beschreibungen und Anmerkungen

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **Annotation** | `string` | 🟢 Optional | Anmerkung | `'Routinekontrolle'` |
| **InputAnnotation** | `string` | 🟢 Optional | Eingabe-Anmerkung | `'Automatisch erstellt'` |
| **SendingAnnotation** | `string` | 🟢 Optional | Versand-Anmerkung | - |
| **ArchiveAnnotation** | `string` | 🟢 Optional | Archiv-Anmerkung | - |

### Klassifikation und Verschlagwortung

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **ObjectType** | `string` | 🟢 Optional | Objekttyp | `'Kontrollbericht'` |
| **ActivityType** | `string` | 🟢 Optional | Aktivitätstyp | `'Lebensmittelkontrolle'` |
| **SendingType** | `string` | 🟢 Optional | Versandart | `'Elektronisch'` |
| **Keyword** | `Array<string>` | 🟢 Optional | Schlagwörter | `['Hygiene', 'HACCP']` |
| **CatchWord** | `Array<string>` | 🟢 Optional | Stichwörter | `['Kontrolle', 'Bericht']` |

### Archivierung und Aufbewahrung

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **PhysicalObject** | `string` | 🟢 Optional | Physisches Objekt | - |
| **CassationPeriod** | `Integer` | 🟢 Optional | Kassationsfrist (Jahre) | `10` |
| **PlannedCassationDate** | `Date` | 🟢 Optional | Geplante Kassation | `2035-01-27` |
| **ArchiveDate** | `Date` | 🟢 Optional | Archivierungsdatum | - |

### Termine und Fristen

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **DueDate** | `DueDateType` | 🟢 Optional | Fälligkeitsdatum | siehe DueDateType |
| **Term** | `Array<TermType>` | 🟢 Optional | Termine | siehe TermType |

### Beteiligte und Standorte

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **Participants** | `Array<ParticipantsType>` | 🟢 Optional | Beteiligte Personen | siehe ParticipantsType |
| **Locations** | `Array<LocationsType>` | 🟢 Optional | Standorte | siehe LocationsType |
| **Receiver** | `Array<ReceiverType>` | 🟢 Optional | Empfänger | siehe ReceiverType |

### Versionierung

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **Version** | `VersionType` | 🟢 Optional | Versionsinformation | siehe VersionType |

### Verschlüsselung und Sicherheit

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **Closure** | `Closure` | 🟢 Optional | Verschluss/Sicherheit | siehe Closure |

---

## IdentificationType (Identifikation)

### Grundlegende Identifikation

| Feld | Typ | Status | Aktueller Wert | Beschreibung |
|------|-----|--------|----------------|--------------|
| **Identification** | `string` | 🔴 Pflicht | `ABKB` | Hauptidentifikation (BKB-Nummer) |

### Erweiterte Identifikationsfelder

| Feld | Typ | Status | Beschreibung | Beispielwert |
|------|-----|--------|--------------|--------------|
| **ManagementIndicator** | `string` | 🟢 Optional | Verwaltungskennzeichen | `'LM'` (Lebensmittel) |
| **OrganisationalUnit** | `string` | 🟢 Optional | Organisationseinheit | `'Bezirkshauptmannschaft'` |
| **SubjectArea** | `string` | 🟢 Optional | Sachgebiet | `'Lebensmittelaufsicht'` |
| **PersonalRelation** | `PersonDataType` | 🟢 Optional | Personenbezug | siehe PersonDataType |
| **OrdinalNumber** | `string` | 🟢 Optional | Ordnungszahl | `'001'` |
| **Year** | `Integer` | 🟢 Optional | Jahr | `2025` |
| **ObjectID** | `string` | 🟢 Optional | Objekt-ID | - |
| **SequentialNumber** | `string` | 🟢 Optional | Laufende Nummer | `'001'` |
| **TechnicalID** | `string` | 🟢 Optional | Technische ID | - |

---

## PDF-Dokument Metadaten

### BinaryDocument (Pflichtfelder)

| Feld | Typ | Status | Aktueller Wert | Beschreibung |
|------|-----|--------|----------------|--------------|
| **EmbeddedFileURL** | `string` | 🔴 Pflicht | `ABKB` | Eindeutige URL/ID der PDF-Datei |
| **FileName** | `string` | 🔴 Pflicht | `ExtractFileName(APdfPath)` | Dateiname (z.B. 'Kontrollbericht_123.pdf') |
| **MIMEType** | `string` | 🔴 Pflicht | `'application/pdf'` | MIME-Type für PDF-Dateien |

### Zusätzliche PDF-Eigenschaften

| Eigenschaft | Beschreibung | Empfehlung |
|-------------|--------------|------------|
| **Dateigröße** | Maximale Größe | 50-100 MB Limit beachten |
| **PDF-Version** | PDF-Standard | PDF/A für Langzeitarchivierung |
| **Verschlüsselung** | Passwortschutz | Nicht empfohlen für ELAK |
| **Digitale Signatur** | PDF-Signatur | Über ELAK-Signatur-Mechanismus |

---

## Konfigurationstabelle

### Zu konfigurierende Felder

| Feld | Aktueller Wert | Konfigurierbar über | Beschreibung |
|------|----------------|---------------------|--------------|
| **Layer1.Subject** | `'Kontrollbericht'` | Code | Dokumenttyp |
| **Layer1.MainDocument** | `ABKB` | Parameter | Verweis auf PDF |
| **Layer1.MetaData.Identifier.Identification** | `ABKB` | Parameter | BKB-Nummer |
| **Layer1.MetaData.ReferencedIdentifier.Identification** | `ABKB` | Parameter | BKB-Referenz |
| **Layer0.id** | `ABKB` | Parameter | Dokument-ID |
| **Layer0.MetaData.Identifier.Identification** | `ABKB` | Parameter | BKB-Nummer |
| **Layer0.MetaData.ReferencedIdentifier.Identification** | `ABKB` | Parameter | BKB-Referenz |
| **BinaryDocument.EmbeddedFileURL** | `ABKB` | Parameter | PDF-URL/ID |
| **BinaryDocument.FileName** | `ExtractFileName(APdfPath)` | Parameter | PDF-Dateiname |
| **BinaryDocument.MIMEType** | `'application/pdf'` | Konstante | MIME-Type |

### Erweiterbare Felder (für zukünftige Implementierung)

| Feld | Priorität | Beschreibung | Datenquelle |
|------|-----------|--------------|-------------|
| **MetaData.Date** | 🟡 Hoch | Kontrolldatum | Kontrollsystem |
| **MetaData.CreatedBy** | 🟡 Hoch | Kontrolleur | Benutzersystem |
| **MetaData.OrganisationalUnit** | 🟡 Hoch | Behörde | Konfiguration |
| **MetaData.Keyword** | 🟢 Mittel | Schlagwörter | Kontrollart |
| **MetaData.ActivityType** | 🟢 Mittel | Kontrolltyp | Kontrollsystem |
| **Layer1.BusinessType** | 🟢 Niedrig | Verfahrensart | Konfiguration |
| **MetaData.Participants** | 🟢 Niedrig | Beteiligte | Kontrollsystem |
| **MetaData.Locations** | 🟢 Niedrig | Standorte | Betriebsdaten |

---

## Nächste Schritte

1. **Prüfung der aktuellen Implementierung** durch alle Beteiligten
2. **Festlegung zusätzlicher Pflichtfelder** je nach fachlichen Anforderungen
3. **Konfiguration der erweiterbaren Felder** basierend auf verfügbaren Datenquellen
4. **Test-Implementierung** mit erweiterten Metadaten
5. **Dokumentation der finalen Konfiguration**

---

## Kontakt und Feedback

Bitte verwenden Sie diese Dokumentation als Arbeitsgrundlage und markieren Sie:
- ✅ Felder, die implementiert werden sollen
- ❌ Felder, die nicht benötigt werden  
- ❓ Felder, die weitere Klärung benötigen
- 💡 Zusätzliche Anforderungen oder Anmerkungen

**Letzte Aktualisierung:** 2025-01-27

---

## Anhang: Komplexe Datentypen

### PersonDataType

| Feld | Typ | Beschreibung | Beispielwert |
|------|-----|--------------|--------------|
| **GivenName** | `string` | Vorname | `'Max'` |
| **FamilyName** | `string` | Nachname | `'Mustermann'` |
| **DateOfBirth** | `Date` | Geburtsdatum | `1980-01-15` |
| **Identification** | `Array` | Identifikationen | siehe IdentificationType |
| **Address** | `AddressType` | Adresse | siehe AddressType |
| **Telephone** | `Array<string>` | Telefonnummern | `['+43 1 12345']` |
| **Email** | `Array<string>` | E-Mail-Adressen | `['<EMAIL>']` |

### DueDateType

| Feld | Typ | Beschreibung | Beispielwert |
|------|-----|--------------|--------------|
| **Date** | `DateTime` | Fälligkeitsdatum | `2025-02-15T23:59:59Z` |
| **Annotation** | `string` | Anmerkung zur Frist | `'Nachkontrolle erforderlich'` |

### TermType

| Feld | Typ | Beschreibung | Beispielwert |
|------|-----|--------------|--------------|
| **Date** | `DateTime` | Termindatum | `2025-02-01T10:00:00Z` |
| **Annotation** | `string` | Terminbeschreibung | `'Nachkontrolle vor Ort'` |

### VersionType

| Feld | Typ | Beschreibung | Beispielwert |
|------|-----|--------------|--------------|
| **VersionNumber** | `string` | Versionsnummer | `'1.0'` |
| **VersionDate** | `DateTime` | Versionsdatum | `2025-01-27T10:30:00Z` |
| **ReferencedVersion** | `string` | Referenzierte Version | `'0.9'` |
| **Annotation** | `string` | Versionsanmerkung | `'Finale Version'` |

### ParticipantsType

| Feld | Typ | Beschreibung | Beispielwert |
|------|-----|--------------|--------------|
| **Participant** | `PersonDataType` | Teilnehmer | siehe PersonDataType |
| **TypeOfParticipation** | `string` | Art der Teilnahme | `'Kontrolleur'` |
| **DispatchType** | `string` | Versandart | `'E-Mail'` |
| **ReportingType** | `string` | Berichtsart | `'Vollständig'` |
| **TranscriptRemark** | `string` | Protokollbemerkung | - |

### LocationsType

| Feld | Typ | Beschreibung | Beispielwert |
|------|-----|--------------|--------------|
| **Location** | `LocationType` | Standort | siehe LocationType |
| **TypeOfLocation** | `string` | Standorttyp | `'Betriebsstätte'` |
| **Description** | `string` | Beschreibung | `'Hauptküche'` |

### ReceiverType

| Feld | Typ | Beschreibung | Beispielwert |
|------|-----|--------------|--------------|
| **Person** | `PersonDataType` | Empfängerperson | siehe PersonDataType |
| **ReferencedObjects** | `Array<string>` | Referenzierte Objekte | - |

---

## Implementierungshinweise

### Datenvalidierung

```delphi
// Beispiel für Metadaten-Validierung
procedure ValidateMetadata(const AMetaData: MetaDataType);
begin
  if not Assigned(AMetaData.Identifier) then
    raise Exception.Create('Identifier ist Pflichtfeld');

  if AMetaData.Identifier.Identification = '' then
    raise Exception.Create('Identification darf nicht leer sein');

  // Weitere Validierungen...
end;
```

### Erweiterte Konfiguration

```ini
[ELAK_Metadata]
; Layer 1 Konfiguration
Layer1_Subject=Kontrollbericht
Layer1_BusinessType=Lebensmittelkontrolle
Layer1_ObjectType=Kontrollbericht

; Standardwerte für Metadaten
Default_OrganisationalUnit=Lebensmittelaufsicht
Default_ActivityType=Routinekontrolle
Default_CassationPeriod=10

; Schlagwörter (kommagetrennt)
Default_Keywords=Kontrolle,Lebensmittel,Hygiene
```
