﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <Import Condition="Exists('$(BDS)\bin\CodeGear.Deployment.targets')" Project="$(BDS)\bin\CodeGear.Deployment.targets"/>
    <ProjectExtensions>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <ItemGroup Condition="'$(Platform)'=='Android'">
        <DeployFile Include="..\..\wheel.png">
            <RemoteDir>Wait\</RemoteDir>
            <RemoteName>wheel.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
    </ItemGroup>
    <ItemGroup Condition="'$(Platform)'=='iOSDevice'">
        <DeployFile Include="iOSDevice\Debug\Wait.dSYM" Condition="'$(Config)'=='Debug'">
            <RemoteDir>$(PROJECTNAME).app.dSYM\Contents\Resources\DWARF\</RemoteDir>
            <RemoteName>Wait</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_ApplicationIcon_120x120.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_120x120.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_ApplicationIcon_57x57.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_57x57.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SpotlightSearchIcon_80x80.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_80x80.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_ApplicationIcon_114x114.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_114x114.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImageLandscape_2048x1536.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default-Landscape@2x~ipad.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_ApplicationIcon_152x152.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_152x152.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSDevice\Debug\Wait.info.plist" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Info.plist</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="..\..\wheel.png">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>wheel.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SettingIcon_29x29.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SettingIcon_29x29.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImageLandscape_1024x748.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default-Landscape.png</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSDevice\Debug\Wait.entitlements" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Entitlements.plist</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_ApplicationIcon_60x60.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_60x60.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_LaunchImage_320x480.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default.png</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImagePortrait_1536x2008.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName><EMAIL></RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SettingIcon_58x58.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SettingIcon_58x58.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SpotlightSearchIcon_50x50.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_50x50.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_ApplicationIcon_144x144.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_144x144.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_LaunchImage_640x960.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName><EMAIL></RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_SpotlightSearchIcon_29x29.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_29x29.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImageLandscape_2048x1496.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName><EMAIL></RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_SpotlightSearchIcon_40x40.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_40x40.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_ApplicationIcon_76x76.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_76x76.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SpotlightSearchIcon_40x40.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_40x40.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImageLandscape_1024x768.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default-Landscape~ipad.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_ApplicationIcon_72x72.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_72x72.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImagePortrait_1536x2048.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default-Portrait@2x~ipad.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSDevice\Debug\Wait" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Wait</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
            <Required>True</Required>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImagePortrait_768x1004.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default~ipad.png</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SpotlightSearchIcon_100x100.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_100x100.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImagePortrait_768x1024.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default-Portrait~ipad.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_LaunchImage_640x1136.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName><EMAIL></RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSDevice\Debug\ResourceRules.plist" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>ResourceRules.plist</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
    </ItemGroup>
    <ItemGroup Condition="'$(Platform)'=='Win32'">
        <DeployFile Include="..\..\wheel.png">
            <RemoteDir>Wait\</RemoteDir>
            <RemoteName>wheel.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
    </ItemGroup>
    <ItemGroup Condition="'$(Platform)'=='OSX32'">
        <DeployFile Include="$(BDS)\Redist\osx32\libcgunwind.1.0.dylib">
            <RemoteDir>Wait.app\Contents\MacOS\</RemoteDir>
            <RemoteName>libcgunwind.1.0.dylib</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
    </ItemGroup>
    <ItemGroup Condition="'$(Platform)'=='Win64'"/>
    <ItemGroup Condition="'$(Platform)'=='iOSSimulator'">
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_ApplicationIcon_144x144.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_144x144.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_LaunchImage_640x1136.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName><EMAIL></RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImagePortrait_1536x2008.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName><EMAIL></RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImagePortrait_768x1004.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default~ipad.png</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_ApplicationIcon_76x76.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_76x76.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSSimulator\Debug\Wait" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Wait</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
            <Required>True</Required>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_LaunchImage_320x480.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default.png</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_SpotlightSearchIcon_29x29.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_29x29.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSSimulator\Base\Wait.info.plist" Condition="'$(Config)'=='Base'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Info.plist</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSSimulator\Debug\Wait.info.plist" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Info.plist</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_ApplicationIcon_114x114.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_114x114.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SpotlightSearchIcon_80x80.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_80x80.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_ApplicationIcon_57x57.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_57x57.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_ApplicationIcon_120x120.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_120x120.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="..\..\wheel.png">
            <RemoteDir>Wait.app\StartUp\Documents\</RemoteDir>
            <RemoteName>wheel.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImageLandscape_2048x1496.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName><EMAIL></RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_ApplicationIcon_152x152.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_152x152.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SpotlightSearchIcon_40x40.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_40x40.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSSimulator\Debug\Wait.entitlements" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Entitlements.plist</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SpotlightSearchIcon_100x100.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_100x100.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSSimulator\Base\Wait" Condition="'$(Config)'=='Base'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Wait</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
            <Required>True</Required>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_SpotlightSearchIcon_40x40.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_40x40.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSSimulator\Base\Wait.entitlements" Condition="'$(Config)'=='Base'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Entitlements.plist</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_ApplicationIcon_60x60.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_60x60.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SettingIcon_58x58.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SettingIcon_58x58.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="iOSSimulator\Debug\Wait.rsm" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Wait.rsm</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImageLandscape_1024x748.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default-Landscape.png</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImagePortrait_768x1024.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default-Portrait~ipad.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\Redist\osx32\libcgunwind.1.0.dylib">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>libcgunwind.1.0.dylib</RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImagePortrait_1536x2048.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default-Portrait@2x~ipad.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SpotlightSearchIcon_50x50.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SpotlightSearchIcon_50x50.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_SettingIcon_29x29.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_SettingIcon_29x29.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImageLandscape_1024x768.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default-Landscape~ipad.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPhone\FM_LaunchImage_640x960.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName><EMAIL></RemoteName>
            <Operation>1</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_LaunchImageLandscape_2048x1536.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>Default-Landscape@2x~ipad.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
        <DeployFile Include="$(BDS)\bin\Artwork\iOS\iPad\FM_ApplicationIcon_72x72.png" Condition="'$(Config)'=='Debug'">
            <RemoteDir>Wait.app\</RemoteDir>
            <RemoteName>FM_ApplicationIcon_72x72.png</RemoteName>
            <Operation>0</Operation>
            <LocalCommand/>
            <RemoteCommand/>
            <Overwrite>True</Overwrite>
        </DeployFile>
    </ItemGroup>
</Project>
