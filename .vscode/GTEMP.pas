unit ElakSendPDF;

interface

uses
  System.SysUtils,
  System.Classes,
  System.Generics.Collections,
  System.NetEncoding,
  System.Zip,
  System.IOUtils,

  Soap.InvokeRegistry,
  Soap.Rio,
  Soap.SOAPHTTPClient,

  DX.Utils.Logger,
  ElakConfig,

//  ElakTrans_SendData_TLB;
ELAK_WSDL;

type
  TSOAPLogger = class
    class procedure LogRequest(const MethodName: string; RequestStream: TStream);
    class procedure LogResponse(const MethodName: string; ResponseStream: TStream);
  end;

procedure SendPDFToElak(const APdfPath:string; ABKB: string);

implementation

function EncodeZipWithPDF(const APDFPath, ADocID: string): TArray<System.Byte>;
var
  LZipStream: TMemoryStream;
  LZip: TZipFile;
  LEncoded: TBytes;
begin
  LZipStream := TMemoryStream.Create;
  try
    LZip := TZipFile.Create;
    try
      LZip.Open(LZipStream, zmWrite);
      LZip.Add(APDFPath, ADocID + ExtractFileExt(APDFPath));
      LZip.Close;
    finally
      LZip.Free;
    end;
    LZipStream.Position := 0;
    SetLength(LEncoded, LZipStream.Size);
    LZipStream.ReadBuffer(LEncoded[0], LZipStream.Size);
    Result := LEncoded;
  finally
    LZipStream.Free;
  end;
end;

procedure SendPDFToElak(const APdfPath:string; ABKB: string);
var
  LHTTPRIO: THTTPRIO;
  LWS: ElakTrans;
  LInput: SendDataInputObject;
  LDocID: string;
  LLayer1: Layer1Type;
  LLayer0: Layer0Type;
  LPayload2: Payload2;
  LPayload3: Payload3;
  LPayload4: Payload4;
  LConfig: TElakConfig;
  LServiceURL: string;
begin
  LDocID := '';
  LInput := nil;
  // Layer objects will be automatically managed by SOAP framework

  // Konfiguration laden
  LConfig := TElakConfig.Instance;
  LServiceURL := LConfig.GetServiceURL;

  DXLog('ELAK Service URL: ' + LServiceURL, TLogLevel.Info);

  LHTTPRIO := THTTPRIO.Create(nil);

  // SOAP Logging nur aktivieren wenn in Konfiguration aktiviert
  if LConfig.IsSOAPLoggingEnabled then
  begin
    LHTTPRIO.OnBeforeExecute := TSOAPLogger.LogRequest;
    LHTTPRIO.OnAfterExecute := TSOAPLogger.LogResponse;
  end;

  // Service mit konfigurierter URL initialisieren
  LWS := GetElakTrans(False, LServiceURL, LHTTPRIO);
  LInput := SendDataInputObject.Create;
  try
    // Initialize CommonInputParameter with required values
    LInput.CommonInputParameter := CommonInputParameterType.Create;
    LInput.CommonInputParameter.SourceSystemId := LConfig.GetSourceSystemId; //Default: ELKE
    LInput.CommonInputParameter.VKZType := 'L2.A10';  // Sachgebiet als VKZ (Verfahrenskennzahl) //Norbert erstellt dafür Mapping
    LInput.CommonInputParameter.Procedure_ := LConfig.GetDefaultProcedure; //Default: ELAK.IMPORT

    // Set up request identifier with a new GUID and sequence number
    LInput.CommonInputParameter.RequestIdentifier := RequestIdentifierType.Create;
    LInput.CommonInputParameter.RequestIdentifier.ConversationIdentifier := TGUID.NewGuid.ToString;
    LInput.CommonInputParameter.RequestIdentifier.SequenceIdentifier := '1';

    // Set the purpose of the request
    LInput.Purpose := PurposeType.INPUT;

    // Configure layer controls to specify what data we're sending
    // Only Layer1 and Layer0 are used (Layer3 and Layer2 are skipped)
    LInput.LayerControl := LayerControlType.Create;

    // Layer 1 (Dokument) control settings
    LInput.LayerControl.Layer1SendControl := LayerSendControlType.Create;
    LInput.LayerControl.Layer1SendControl.ContainsModifiedBasicData := True;

    // Layer 0 (Binärdokument) control settings
    LInput.LayerControl.Layer0SendControl := LayerSendControlType.Create;
    LInput.LayerControl.Layer0SendControl.ContainsModifiedBasicData := True;
    LInput.LayerControl.Layer0SendControl.ContainsModifiedBinaryContent := True;

    LDocID := ABKB;
    LInput.Documents := EncodeZipWithPDF(APdfPath, LDocID);

    LInput.Ediakt := EdiaktType.Create;
    LInput.Ediakt.Header := HeaderType.Create;
    LInput.Ediakt.MetaData := MetaDataType.Create;
    LInput.Ediakt.MetaData.Identifier := IdentificationType.Create;
    LInput.Ediakt.MetaData.ReferencedIdentifier := IdentificationType.Create;

    // Initialize Payload directly with Layer1 (skipping Layer3 and Layer2)
    // EdiaktType.Payload is Payload4, which has Layer1 property of type Payload2 (array of Layer1Type)
    LPayload4 := Payload4.Create;
    LLayer1 := Layer1Type.Create;
    // Create the payload as an array with one element (Payload2 is array of Layer1Type)
    SetLength(LPayload2, 1);
    LPayload2[0] := LLayer1;
    // Assign Layer1 payload to Payload4 and then to EdiaktType
    LPayload4.Layer1 := LPayload2;
    LInput.Ediakt.Payload := LPayload4;

    // Configure Layer1
    LLayer1.Subject := 'Kontrollbericht';
    LLayer1.MetaData := MetaDataType.Create;
    LLayer1.MetaData.Identifier := IdentificationType.Create;
    LLayer1.MetaData.ReferencedIdentifier := IdentificationType.Create;
    LLayer1.MetaData.ReferencedIdentifier.Identification := ABKB; //Betrieb / Kontrolltyp 

    LLayer1.MainDocument := LDocID;

    // Initialize Payload3 which is an array of Layer0Type
    LLayer0 := Layer0Type.Create;
    // Create and assign the payload as an array with one element
    SetLength(LPayload3, 1);
    LPayload3[0] := LLayer0;
    LLayer1.Payload := LPayload3;

    // Configure Layer0
    LLayer0.Id := LDocID;
    LLayer0.MetaData := MetaDataType.Create;
    LLayer0.MetaData.Identifier := IdentificationType.Create;
    LLayer0.MetaData.ReferencedIdentifier := IdentificationType.Create;
    LLayer0.MetaData.ReferencedIdentifier.Identification := ABKB;
    LLayer0.Payload := PayloadType.Create;
    LLayer0.Payload.BinaryDocument := BinaryDocument.Create;
    LLayer0.Payload.BinaryDocument.EmbeddedFileURL := LDocID;
    LLayer0.Payload.BinaryDocument.FileName := ExtractFileName(APdfPath); //50MB 100MB Limitation
    LLayer0.Payload.BinaryDocument.MIMEType := 'application/pdf';

    try
      LWS.SendData(LInput);
    except
      on E: Exception do
        raise Exception.Create('Fehler beim Senden an ELAK: ' + E.Message);
    end;
  finally
    // Clean up objects
    // Note: Layer objects (LLayer0, LLayer1) are automatically freed
    // by their parent objects' destructors when LInput is freed.
    // HTTPRIO is managed automatically through interface reference counting (ElakTrans interface).
    // Manual freeing would cause AccessViolation due to double-free.
    FreeAndNil(LInput);  // This will cascade-free all Layer objects
  end;
end;

class procedure TSOAPLogger.LogRequest(const MethodName: string; RequestStream: TStream);
var
  LStrings: TStringList;
begin
  LStrings := TStringList.Create;
  try
    RequestStream.Position := 0;
    LStrings.LoadFromStream(RequestStream, TEncoding.UTF8);
    DXLog('SOAP Request (%s): %s', [MethodName, LStrings.Text], TLogLevel.Debug);
  finally
    LStrings.Free;
  end;
end;

class procedure TSOAPLogger.LogResponse(const MethodName: string; ResponseStream: TStream);
var
  LStrings: TStringList;
begin
  LStrings := TStringList.Create;
  try
    ResponseStream.Position := 0;
    LStrings.LoadFromStream(ResponseStream, TEncoding.UTF8);
    DXLog('SOAP Response (%s): %s', [MethodName, LStrings.Text], TLogLevel.Debug);
  finally
    LStrings.Free;
  end;
end;

end.

