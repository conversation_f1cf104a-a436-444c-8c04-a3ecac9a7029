<?xml version="1.0" encoding="UTF-8"?>

<wsdl:definitions targetNamespace="http://reference.e-government.gv.at/namespace/elaktrans/3#"
                  xmlns="http://reference.e-government.gv.at/namespace/elaktrans/3#"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/">

  <!-- File: ElakTrans.xsd Version: 3.0 -->
  <!-- 01.03.2016 Authoren: 
      <PERSON> (Niederösterreich), 
      <PERSON> (Wien), 
      <PERSON> (Salzburg, Magistrat)
      <PERSON> (Tirol),
      <PERSON> (Wien),
      <PERSON> (Oberösterreich)
      <PERSON> (Oberösterreich)
  -->
  <!-- (c) 2015 - 2016 / ELAK-TRANS Arbeitsgruppe -->

  <wsdl:documentation>
    Dieses WSDL-Dokument beschreibt den Transfer
    von Akten, Geschäftsfällen und -stücken zwischen verschiedenen
    EDIAKT-Systemen.
  </wsdl:documentation>

  <wsdl:types>

    <xsd:schema attributeFormDefault="unqualified"
                elementFormDefault="qualified"
                targetNamespace="http://reference.e-government.gv.at/namespace/elaktrans/3#"
                version="1.0.0"
                xmlns:edi="http://reference.e-government.gv.at/namespace/edidoc/20130808#"
                xmlns:xs="http://www.w3.org/2001/XMLSchema">

      <xsd:include schemaLocation="ElakTrans_30.xsd" />

      <!-- Exception Handling lt. ReferenceServer - SOAP-Faults v1.0.6 -->
      <!--
      <xsd:element name="Fault">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="faultcode" type="xs:QName" />
            <xsd:element name="faultstring" type="xs:string" />
            <xsd:element name="faultactor" type="xs:anyURI" />
            <xsd:element name="detail">
              <xsd:complexType>
                <xsd:sequence>
                  <xsd:any maxOccurs="unbounded" minOccurs="0" namespace="##any" processContents="lax" />
                </xsd:sequence>
              </xsd:complexType>
            </xsd:element>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      -->

    </xsd:schema>

  </wsdl:types>

  <wsdl:message name="SendDataInput">
    <wsdl:part name="SendDataInputObject" element="SendDataInputObject">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="SendDataOutput">
    <wsdl:part name="SendDataOutputObject" element="SendDataOutputObject">
    </wsdl:part>
  </wsdl:message>

  <wsdl:message name="ReadDataInput">
    <wsdl:part name="ReadDataInputObject" element="ReadDataInputObject">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="ReadDataOutput">
    <wsdl:part name="ReadDataOutputObject" element="ReadDataOutputObject">
    </wsdl:part>
  </wsdl:message>

  <wsdl:message name="UnbindDataInput">
    <wsdl:part name="UnbindDataInputObject" element="UnbindDataInputObject">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UnbindDataOutput">
    <wsdl:part name="UnbindDataOutputObject" element="UnbindDataOutputObject">
    </wsdl:part>
  </wsdl:message>

  <wsdl:portType name="ElakTrans">

    <!--
    Die SendData-Methode dient dazu, ausgehend vom FIS folgende Use Cases abzudecken:
    
    FIS-2-ELAK (aus der Sicht des Clients)
    ======================================
    a) Aktenstruktur (Geschäftsobjekte) erzeugen
    b) Aktenstruktur (Geschäftsobjekte) aktualisieren
    c) Geschäftsstück verschieben 
    
    FIS-2-ELAK (aus der Sicht des Servers)
    ======================================
    a) Akt, Geschäftsfall, Geschäftsstück und/oder Dokument erzeugen
    b) Akt, Geschäftsfall, Geschäftsstück und/oder aktualisieren
       (inkludiert z.B. auch Statusaktualisierungen wie das Abschließen des Akts)
    c) Geschäftsstück (Eingangsstück, Ausgangsstück) umprotokollieren
    
    Welcher Use Case ausgelöst wird, hängt von der Interpretation der übermittelten Objekte durch die ELAK-TRANS
    Server-Implementierung ab. Je nach zugrunde liegendem ELAK-System und den Vorgaben für den Aufbau und die
    Struktur der Akten kann die Übermittlung des selben SendDataInputObjects an unterschiedliche Server zu
    unterschiedlichen Ergebnissen in den ELAK-Systemen führen.
    
    ELAK-2-FIS (aus der Sicht des Clients)
    ======================================
    a) Daten aktualisieren (L3 - L0)
    b) Geschäftsstück/"Aufgabe" übermitteln (L1)
    c) Benachrichtigung über Status-Aktualisierung (L3, L1)
    d) Benachrichtigung über Zustellstatus-Aktualisierung (L1)
    
    ELAK-2-FIS (aus der Sicht des Servers)
    ======================================
    a) FIS-L3-, FIS-L2-, FIS-L1- und/oder FIS-L0-Entität aktualisieren
    b) Geschäftsstück/"Aufgabe" erzeugen (L1)
    c) Status-Aktualisierung verarbeiten (L3, L1)
    d) Zustellstatus-Aktualisierung verarbeiten (L1)
    
    Welche fachlichen FIS Use Cases ausgelöst werden, hängt von der Interpretation der übermittelten Objekte
    durch die ELAK-TRANS ELAK-2-FIS Server-Implementierung (im FIS) ab. Das FIS verarbeitet die Requests nach
    seinen fachlichen Vorgaben und Regeln.
    -->
    <wsdl:operation name="SendData">
      <wsdl:input name="SendDataInput" message="SendDataInput">
      </wsdl:input>
      <wsdl:output name="SendDataOutput" message="SendDataOutput">
      </wsdl:output>
    </wsdl:operation>

    <!--
    FIS-2-ELAK
    ==========
    Die ReadData-Methode dient dazu, ausgehend vom FIS Aktenstrukturen/Geschäftsobjekte im ELAK zu lesen oder eine URI
    auf gewisse Geschäftsobjekte im ELAK zu erhalten. Je nach ELAK-TRANS FIS-2-ELAK Server-Implementierung und Aufbau 
    des ELAKs kann es beim Lesen der Objekte zu unterschiedlichen Ergebnissen kommen, sodass das mittels ReadData
    gelesene EDIDOC-Paket eventuell nicht vollständig einem zuvor übermittelten SendData-ECIDOC-Paket entspricht (z.B.
    ein Entfallen von SpecialData-Knoten). Gewisse Metadaten sind allerdings "genormt" und werden von allen Systemen 
    im selben Knoten geliefert (z.B. Aktenzahl, URI, ...)
    
    ELAK-2-FIS
    ==========
    Die ReadData-Methode dient dazu, ausgehend vom ELAK URIs für FIS-Entitäten zu erhalten, welche mit ELAK-
    Aktenstrukturen verküpft sind. Der ELAK kann diese z.B. dafür verwenden, in seinem UI die Möglcihkeit zu bieten,
    in einem weiteren Browserfenster oder -tab das FIS mit den zum Kontext passenden FIS-Entitäten zu öffnen.
    -->
    <wsdl:operation name="ReadData">
      <wsdl:input name="ReadDataInput" message="ReadDataInput">
      </wsdl:input>
      <wsdl:output name="ReadDataOutput" message="ReadDataOutput">
      </wsdl:output>
    </wsdl:operation>

    <!--
    Mit der Unbind-Methode informiert das FIS den ELAK darüber, dass die Verbindung zum FIS für das angegebene Objekt
    gelöst werden soll. Das FIS kann nach Aufruf dieser Methode das Objekt im ELAK nicht mehr korrekt adressieren. 
    Diese Methode dient vor allem der Möglichkeit, Anwender-Fehler zu korrigieren, die durch die Umsetzung der 
    Bidirektionalität entstehen können.
    -->
    <wsdl:operation name="UnbindData">
      <wsdl:input name="UnbindDataInput" message="UnbindDataInput">
      </wsdl:input>
      <wsdl:output name="UnbindDataOutput" message="UnbindDataOutput">
      </wsdl:output>
    </wsdl:operation>

  </wsdl:portType>

  <wsdl:binding name="ElakTransSoapBinding" type="ElakTrans">

    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

    <wsdl:operation name="SendData">
      <soap:operation soapAction="http://reference.e-government.gv.at/namespace/elaktrans/3#/SendData" />
      <wsdl:input name="SendDataInput">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="SendDataOutput">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>

    <wsdl:operation name="ReadData">
      <soap:operation soapAction="http://reference.e-government.gv.at/namespace/elaktrans/3#/ReadData" />
      <wsdl:input name="ReadDataInput">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ReadDataOutput">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>

    <wsdl:operation name="UnbindData">
      <soap:operation soapAction="http://reference.e-government.gv.at/namespace/elaktrans/3#/UnbindData" />
      <wsdl:input name="UnbindDataInput">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="UnbindDataOutput">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>

  </wsdl:binding>

  <wsdl:service name="ElakTransService">
    <wsdl:port name="ElakTransService" binding="ElakTransSoapBinding">
      <soap:address location="http://localhost/at.gv.noe.elaktrans30servicestest/ElakTransService.svc" />
    </wsdl:port>
  </wsdl:service>

</wsdl:definitions>
