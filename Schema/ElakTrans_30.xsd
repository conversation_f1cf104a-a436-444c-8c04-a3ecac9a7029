<?xml version="1.0" encoding="UTF-8"?>

<!-- File: ElakTrans.xsd Version: 3.0 -->
<!-- 01.03.2016 Authoren: 
      <PERSON> (Niederösterreich), 
      <PERSON> (Wien), 
      <PERSON> (Salzburg, Magistrat)
      <PERSON> (Tirol),
      <PERSON> (Wien),
      <PERSON> (Oberösterreich)
      <PERSON> (Oberösterreich)
-->
<!-- (c) 2015 - 2016 / ELAK-TRANS Arbeitsgruppe -->

<xsd:schema attributeFormDefault="unqualified"
            elementFormDefault="qualified"
            targetNamespace="http://reference.e-government.gv.at/namespace/elaktrans/3#"
            version="1.0"
            xmlns="http://reference.e-government.gv.at/namespace/elaktrans/3#"
            xmlns:edi="http://reference.e-government.gv.at/namespace/edidoc/20130808#"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema">

  <xsd:import namespace="http://reference.e-government.gv.at/namespace/edido<PERSON>/20130808#"
              schemaLocation="import/edidoc-1.0.xsd" />

  <xsd:element name="SendDataInputObject">
    <xsd:annotation>
      <xsd:documentation>
        Input-Objekt für die Service-Operation "SendData". Diese Struktur ist durch Fusion der ELAK-TRANS-2.0-Elemente 
        "insertElakInputObject", "updateElakInputObject", "insertApplicationInputObject" und 
        "updateApplicationInputObject" entstanden.
      </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="CommonInputParameter" type="CommonInputParameterType" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Dokumentation siehe direkt beim Typ.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="Purpose" type="PurposeType" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Dieser Vermerk bezieht sich immer auf den Layer 1 - das Geschäftsstück. Weitere Dokumentation siehe
              direkt beim Typ.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="LayerControl" type="LayerControlType" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Dokumentation siehe direkt beim Typ.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="Ediakt" type="edi:EdiaktType" minOccurs="1" maxOccurs="1" />
        <xsd:element name="Documents" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              ZIP-File mit allen Dateianhängen ohne Ordnerstruktur. Die Verbindung zwischen Ediakt/Layer0/Payload- 
              und Documents-Konten ist in den Spezifikations- bzw. Best-Practise-Dokumenten beschrieben.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="SendDataOutputObject">
    <xsd:annotation>
      <xsd:documentation>
        Output-Objekt für den Service sendData
      </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="CommonOutputParameter" type="CommonOutputParameterType" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Dokumentation siehe direkt beim Typ.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="Ediakt" type="edi:EdiaktType" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              EDIDOC-Aktenstruktur. Diese wird abhängig von den durch die LayerControl ausgewählten Daten befüllt.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="Documents" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              ZIP-File mit allen Dateianhängen ohne Ordnerstruktur. Die Verbindung zwischen Ediakt/Layer0/Payload-
              und Documents-Konten ist in den Spezifikations- bzw. Best-Practise-Dokumenten beschrieben.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="ReadDataInputObject">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="CommonInputParameter" type="CommonInputParameterType" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Dokumentation siehe direkt beim Typ.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="LayerControl" type="LayerControlType" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Mittels der LayerResponseControl in der LayerControl wird festgelegt, welche Objekte in der EDIAKT-/
              EDIDOC-Struktur der Response überhaupt enthalten sind (Selektion), und welche Daten bzw. Details der
              über die Selektion festgelegten Objekte zurückgeliefert werden sollen (Projektion).
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="ReadDataOutputObject">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="CommonOutputParameter" type="CommonOutputParameterType" />
        <xsd:element name="Ediakt" type="edi:EdiaktType">
          <xsd:annotation>
            <xsd:documentation>
              EDIAKT-Objekt: Die EDIAKT-Struktur wird abhängig von den ausgewählten Daten entsprechend befüllt;
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="Documents" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              ZIP-File mit allen Dateianhängen ohne Ordnerstruktur. Die Verbindung zwischen Ediakt/Layer0/Payload-
              und Documents-Konten ist in den Spezifikations- bzw. Best-Practise-Dokumenten beschrieben.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="UnbindDataInputObject">
    <xsd:annotation>
      <xsd:documentation>
        Input-Objekt für den Service UnbindData
      </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="CommonInputParameter" type="CommonInputParameterType" minOccurs="1" maxOccurs="1" />
        <xsd:element name="LayerControl" type="LayerControlType" minOccurs="1" maxOccurs="1" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="UnbindDataOutputObject">
    <xsd:annotation>
      <xsd:documentation>
        Output-Objekt für den Service UnbindData
      </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="CommonOutputParameter" type="CommonOutputParameterType" minOccurs="1" maxOccurs="1" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:complexType name="CommonInputParameterType">
    <xsd:annotation>
      <xsd:documentation>
        Allgemeiner Schnittstellen-Parameter für sämtliche Requests. Damit werden die ursprünglichen ELAK-Trans-2.0-
        Typen "commonApplicationInputParameter" und "commonELAKInputParameter" fusioniert.
      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence minOccurs="1" maxOccurs="1">
      <xsd:element name="SourceSystemId" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Eindeutige Kennung des Quellsystems (ELAK oder FIS, z.B. "at.gv.noe.elak" bzw. "at.gv.ooe.sotra")
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DestinationSystemId" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Eindeutige Kennung des Zielsystems (ELAK oder FIS, z.B. "at.gv.noe.elak" bzw. "at.gv.ooe.sotra")
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="VKZType" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Verwaltungskennzeichen, welches die Behörde des Empfängers definiert (z.B. "L4AL-IT")
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Procedure" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Leistungskürzel des Verfahrens laut Verfahrenstabelle (ELKAT; 
            ﻿https://egov.stmk.gv.at/lavi/rlavi/showBereiche.do); das Leistungskürzel kann z.B. zur Feststellung des 
            Sachgebiets in der ELAK-Trans-Middleware verwendet werden.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RequestIdentifier" type="RequestIdentifierType" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Identifier für 
            a) systemübergreifende Fehlersuche und Logging und 
            b) Ablaufsteuerung bei asynchroner multi-threaded Massenverarbeitung und Zuordnung von Requests und Responses.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DebugMode" type="xsd:boolean" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Damit kann man angeben, in welchem Detailgrad der Server Fehlermeldungen, Warnungen und Informationen
            zurückliefern soll. Wenn der Wert "true" ist, also wenn man etwa gerade einen Client im Testsystem 
            entwickelt, dann will man sinnvollerweise alle am Server verfügbaren Informationen, um das Verhalten 
            desselben besser nachvollziehen zu können. Ist das Element nicht spezifiziert, so ist dies gleichbedeutend, 
            als wäre der Wert "false".
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="CommonOutputParameterType">
    <xsd:annotation>
      <xsd:documentation>
        Allgemeiner Schnittstellen-Parameter für sämtliche Responses. Damit werden die ursprünglichen ELAK-Trans-2.0-
        Typen "commonApplicationOutputParameter" und "commonElakOutputParameter" fusioniert.
      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="RelatedRequestIdentifier" type="RequestIdentifierType" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Identifier für 
            a) systemübergreifende Fehlersuche und Logging und 
            b) Ablaufsteuerung bei asynchroner multi-threaded Massenverarbeitung und Zuordnung von Requests und Responses.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UserMessage" type="UserMessageType" minOccurs="0" maxOccurs="1" />
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="RequestIdentifierType">
    <xsd:annotation>
      <xsd:documentation>
        Der RequestIdentifier soll bei der system- und organisationsübergreifenden Fehlersuche helfen. Außerdem
        ermöglicht er eine Ablaufsteuerung bei asynchroner Multi-Thread-Massenverarbeitung, dabei insbesondere die
        Zuordnung von Responses zu den entsprechenden Requests.
      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence minOccurs="1" maxOccurs="1">
      <xsd:element name="ConversationIdentifier" type="xsd:token" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Die Conversation-ID muss innerhalb der SourceSystem-ID eindeutig sein.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SequenceIdentifier" type="xsd:token" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Die Sequence-ID muss innerhalb der Conversation-ID eindeutig und vor allem aufsteigend vergeben werden, um
            eine definierte Reihenfolge der "Requests" bei der Abarbeitung sicherstellen zu können.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="UserMessageType">
    <xsd:annotation>
      <xsd:documentation>
        Bietet die Möglichkeit, dem Benutzer in einer für ihn lesbaren Art und Weise Rückmeldung darüber zu geben, was
        auf der Server-Seite passiert ist. Sämtliche Nachrichten - falls mehrere erwünscht sind - werden in einer
        Zeichenkette formatiert.
      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence minOccurs="1" maxOccurs="1">
      <xsd:element name="Message" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Sämtliche Nachrichten werden hier in einer Zeichenkette formatiert. Die aufrufende Anwendung kann dann - 
            falls gewünscht - diese retournierte Zeichenkette einfach im User-Interface anzeigen.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Severity" type="UserMessageSeverityType" minOccurs="1" maxOccurs="1" />
    </xsd:sequence>
  </xsd:complexType>

  <xsd:simpleType name="UserMessageSeverityType">
    <xsd:annotation>
      <xsd:documentation>
        Für normale Benutzerinformationen, also etwa für Rückmeldungen, dass etwas gespeichert wurde, wählt man
        naheliegenderweise den Wert INFORMATION. Sobald sich in den bis zu n zu einer Zeichenkette formatierten
        Rückmeldungen eine Warnung befindet, soll der Wert WARNING verwendet werden. Sprich, der höchste
        Schweregrad entscheidet. Normale und fatale Fehler werden über Exceptions bzw. Faults abgehandelt.
      </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="INFORMATION">
        <xsd:annotation>
          <xsd:documentation>
            Beispiel: "Es wurde ein Akt mit dem Kennzeichen ... erzeugt."
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WARNING">
        <xsd:annotation>
          <xsd:documentation>
            Beispiel: "Der Geschäftsfall ... war bereits abgeschlossen und musste wieder aktiviert werden."
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="PurposeType">
    <xsd:annotation>
      <xsd:documentation>
        Den Wert kann die ELAK-TRANS-Middleware etwa dafür verwenden, um abhängig davon im ELAK verschiedene 
        Objektklassen für das Anlegen des Geschäftsstücks zu verwenden (z.B. Eingangs- oder Ausgangsstück). Auch andere 
        Steuerungen in der Logik lassen sich an dieser Auswahl, eventuell auch in Verbindung mit anderen Elementen, 
        festmachen.
      </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="INPUT">
        <xsd:annotation>
          <xsd:documentation>
            Fabasoft-Kunden wählen hier im FIS-2-ELAK Server üblicherweise das Eingangsstück als Objektklasse für das 
            neu anzulegende Geschäftsstück.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="OUTPUT">
        <xsd:annotation>
          <xsd:documentation>
            Fabasoft-Kunden wählen hier im FIS-2-ELAK Server üblicherweise das Ausgangsstück (in der NÖL alternativ die 
            Zuschrift) als Objektklasse für das neu anzulegende Geschäftsstück.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INTERNAL">
        <xsd:annotation>
          <xsd:documentation>
            Fabasoft-Kunden wählen hier im FIS-2-ELAK Server üblicherweise das interne Stück als Objektklasse für das 
            neu anzulegende Geschäftsstück.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:complexType name="LayerControlType">
    <xsd:annotation>
      <xsd:documentation>
        Mit den Informationen in dieser Struktur kann man einerseits für die SendData-Operation die semantische Bedeutung 
        der Daten innerhalb des EDIAKT-/EDIDOC-Elements im Request konkretisieren. Man kann etwa festlegen, dass im 
        Layer 2 nur die vom Client aktualisierten Adressaten mitgeliefert werden, alle anderen Daten aber unverändert 
        blieben und daher gar nicht mitgeliefert werden. Andererseits kann man für sämtliche ELAK-TRANS-Operationen die 
        Responses genau festlegen; d.h. welche Objekte sollen in der EDIAKT-/EDIDOC-Struktur der Response überhaupt 
        enthalten sein (Selektion), und welche Daten bzw. Details der über die Selektion festgelegten Objekte sollen 
        zurückgeliefert werden (Projektion). Beim UnbindData werden die (FIS-)IDs der Objekte angegeben, für welche die 
        Verknüpfung aufgehoben werden soll.
      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence minOccurs="1" maxOccurs="1">
      <xsd:element name="Layer3SendControl" type="LayerSendControlType" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Dieser Knoten ist nur bei SendData relevant und steuert in diesem Fall, wie die im Layer 3 (Ebene "Akt") 
            der EDIAKT-/EDIDOC-Struktur im Request mitgelieferten Daten zu interpretieren sind. Zunächst kann die 
            Festlegung sehr granular getroffen werden (BasicData, Participants, State, etc.), und für jeden dieser 
            Bereiche kann explizit festgelegt werden, ob im Request veränderte Daten mitgeliefert werden. Faktisch 
            lässt sich mit einer sinnvollen Parametrisierung Laufzeit am Server einsparen, da sich damit implizit 
            schreibende und damit (vor allem im ELAK) "teure" Zugriffe vermeiden lassen.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Layer2SendControl" type="LayerSendControlType" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Dieser Knoten ist nur bei SendData relevant und steuert in diesem Fall, wie die im Layer 2 (Ebene 
            "Geschäftsfall") der EDIAKT-/EDIDOC-Struktur im Request mitgelieferten Daten zu interpretieren sind. Die 
            semantische Bedeutung ist gleich wie jene von "Layer3SendControl".
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Layer1SendControl" type="LayerSendControlType" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Dieser Knoten ist nur bei SendData relevant und steuert in diesem Fall, wie die im Layer 1 (Ebene
            "Geschäftsstück") der EDIAKT-/EDIDOC-Struktur im Request mitgelieferten Daten zu interpretieren sind.
            Die semantische Bedeutung ist gleich wie jene von "Layer3SendControl".
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Layer0SendControl" type="LayerSendControlType" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Dieser Knoten ist nur bei SendData relevant und steuert in diesem Fall, wie die im Layer 0 (Ebene
            "Dokument") der EDIAKT-/EDIDOC-Struktur im Request mitgelieferten Daten zu interpretieren sind.
            Die semantische Bedeutung ist gleich wie jene von "Layer3SendControl". Wesentlich - im Gegensatz zu den
            anderen Layern - ist hier die Festlegung für den Bereich "BinaryContent", denn dort ist wohl die größte
            Performance-Optimierung möglich. Das kann etwa dann interessant sein, wenn nur die Metadaten eines 
            Dokuments aktualisiert werden sollen.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Layer3ResponseControl" type="LayerResponseControlType" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Für jede ELAK-Trans-Operation ist eine mehr oder weniger mit Daten befüllte Response vorgesehen. Welche
            Informationen in der Response mitgeliefert werden sollen, kann man an dieser Stelle für den Layer 3 (Ebene
            "Akt") festlegen, genauer gesagt, für sämtliche in die Response zu inkludierende Objekte des Layers 3.
            Zunächst kann über das Layer3-Element festgelegt werden, welche Objekte des Layers 3 überhaupt in der 
            Response enthalten sein sollen (Selektion). Beim Layer 3 gibt es nur diese eine explizite Möglichkeit der
            Selektion; das kann zwar nicht direkt durch Einschränkungen im Schema erzwungen werden, aber die 
            Spezifikation sieht das vor, und in weiterer Folge werden das die Implementierungen sicherstellen. Für die
            selektierten Objekte des Layers 3 kann festgelegt werden, welche Bereiche bzw. Details (BasicData, 
            Participants, State, etc.) in der Response berücksichtigt werden sollen (Projektion).
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Layer2ResponseControl" type="LayerResponseControlType" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Dieser Knoten legt für den Layer 2 (Ebene "Geschäftsfall") fest, welche Informationen dieser Ebene in der 
            Response mitgeliefert werden sollen. Zunächst kann einerseits implizit über genau spezifizierte und 
            adressierte Layer2-Elemente innerhalb des Layer3-Elements festgelegt werden, welche Objekte des Layers 2 in 
            der Response enthalten sein sollen (Variante 1 der Selektion). Alternativ kann über die Unterelemente
            "IncludeUnspecifiedCoupledObjects" bzw. "IncludeUncoupledObjects" eine Selektion getroffen werden. Die 
            Bedeutung der beiden Elemente wird ebendort speziell erläutert. Über die Selektion hinaus kann wie beim 
            Layer 3 festgelegt werden, welche Informationen der selektierten Objekte berücksichtigt werden sollen 
            (Projektion). 
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Layer1ResponseControl" type="LayerResponseControlType" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Dieser Knoten legt für den Layer 1 (Ebene "Geschäftsstück") fest, welche Informationen dieser Ebene in der
            Response mitgeliefert werden sollen. Die syntaktische Festlegung erfolgt wie im Layer 2 und die Semantik
            entspricht jener des Layers 2.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Layer0ResponseControl" type="LayerResponseControlType" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Dieser Knoten legt für den Layer 0 (Ebene "Dokument") fest, welche Informationen dieser Ebene in der 
            Response mitgeliefert werden sollen. Die syntaktische Festlegung erfolgt wie im Layer 2 und die Semantik
            entspricht ebenso jener des Layers 2. Aber gerade bei diesem Layer ist diese Kontrollstruktur
            so essentiell, geht es doch darum, an dieser Stelle zu entscheiden ob etwa die Inhalte der Layer-0-Objekte
            in der Response enthalten sein sollen und/oder nur die URLs. Das macht im Performance-Verhalten einen
            riesengroßen Unterschied.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Layer3" type="Layer3ControlType" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Hier wird die explizite Selektion des einzigen Layer3-Objekts vorgenommen; für den Layer 3 ist diese implizite 
            Variante nämlich für jeden Anwendungszweck gleichzeitig die einzige. Bei SendData ist die Adressierung 
            prinzipiell über die (parallele und ohnehin vorhandene) EDIAKT-/EDIDOC-Struktur ausreichend, sodass im 
            Standardfall für die Konfiguration des Requests hier nichts getan werden muss. Es gibt aber Sonderfälle, 
            wie zum Beispiel das Umprotokollieren, in denen es notwendig ist, alte und neue IDs mitzugeben; dann ist 
            diese Struktur natürlich relevant. Additiv ist auch bei der SendData-Operation die Response zu 
            konfigurieren, sodass zumindest der Layer 3 aus dieser Perspektive heraus angegeben werden muss. Bei 
            ReadData ist - ebenfalls aufgrund der Response-Konfiguration - zumindest die Angabe eines Layer3-Objekts 
            erforderlich. Die Objekte der hierarchisch darunterliegenden Objekte können entweder ebenfalls explizit
            mithilfe von Layer2-Knoten spezifiziert werden oder alternativ implizit bzw. generisch selektiert werden.
            Bei UnbindData ist die Struktur auch zwingend anzugeben, denn da gibt es keine alternative EDIAKT-/EDIDOC-
            Struktur.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="LayerXControlType">
    <xsd:annotation>
      <xsd:documentation>
        Basistyp für die layer-spezifischen Konstrollstrukturen. Die konkreten Kontrollstrukturen für die Layer 3, 2,
        1 und 0 basieren auf diesem Typ. Gemeinsame Elemente werden hier definiert.
      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence minOccurs="1" maxOccurs="1">
      <xsd:element name="Identifier" type="edi:IdentificationType" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Falls erforderlich, wird hier ein layer-spezifischer ELAK-Schlüssel; das ist im Normalfall ein fachlicher
            ELAK-Schlüssel, wie z.B. das Geschäftszeichen, Kennzeichen, oder wie immer das begrifflich heißt.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IdentifierOld" type="edi:IdentificationType" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Falls erforderlich, wird hier ein vorheriger ELAK-Schlüssel eingetragen; das ist im Normalfall ein 
            fachlicher ELAK-Schlüssel, wie z.B. das Geschäftszeichen, Kennzeichen, oder wie immer das begrifflich heißt.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReferencedIdentifier" type="edi:IdentificationType" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Bei SendData, ReadData und UnbindData wird hier die FIS-ID eingetragen.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReferencedIdentifierOld" type="edi:IdentificationType" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Beim Umprotokollieren mittels SendData wird hier die vorherige FIS-IDs eingetragen. In allen anderen Fällen 
            ist dieser Knoten nicht sinnvoll und somit nicht wirksam.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="Layer3ControlType">
    <xsd:complexContent>
      <xsd:extension base="LayerXControlType">
        <xsd:sequence minOccurs="1" maxOccurs="1">
          <xsd:element name="Layer2" type="Layer2ControlType" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>

  <xsd:complexType name="Layer2ControlType">
    <xsd:complexContent>
      <xsd:extension base="LayerXControlType">
        <xsd:sequence minOccurs="1" maxOccurs="1">
          <xsd:element name="Layer1" type="Layer1ControlType" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>

  <xsd:complexType name="Layer1ControlType">
    <xsd:complexContent>
      <xsd:extension base="LayerXControlType">
        <xsd:sequence minOccurs="1" maxOccurs="1">
          <xsd:element name="Layer0" type="Layer0ControlType" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>

  <xsd:complexType name="Layer0ControlType">
    <xsd:complexContent>
      <xsd:extension base="LayerXControlType">
        <xsd:sequence minOccurs="1" maxOccurs="1" />
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>

  <xsd:complexType name="LayerSendControlType">
    <xsd:sequence minOccurs="1" maxOccurs="1">
      <xsd:annotation>
        <xsd:documentation>
          Mit dieser Struktur teilt der Client dem Server bei der SendData-Operation feingranular mit, welche Daten der
          Request enthält. Dafür wird die Menge aller Daten eines Layers logisch in folgende Teilmengen zerlegt: die
          Basisdaten, die Begriffe, die Ortsbezüge, die Adressaten, die Status, die binären Inhalte und die URL(s). 
          Mithilfe dieser Angaben kann und muss der Server entscheiden, welche verknüpften Objekte aktualisiert werden. 
          Gerade im hochgradig verteilten System - und bei ELAK-TRANS haben wir ein solches - ist diese Möglichkeit der 
          expliziten Festlegung relevant, da mit diesem Mechanismus einerseits die Requests so klein wie möglich 
          gehalten werden können und andererseits die schreibenden Operationen inklusive aller begleitenden Maßnahmen 
          (Locks, etc.) am Server auf ein Minimum reduziert werden können. Will ein Client zum Beispiel nur die Daten 
          der Layer 1 und 0 aktualisieren, so hat er auf diese Art und Weise die Möglichkeit, die Objekte der Layer 3 
          und 2 vor Updates zu bewahren. Oder - anderes Beispiel - möchte der Client etwa nur den Status eines 
          Geschäftsfalls ändern, dann muss braucht er nicht zusätzlich die Basisdaten, die Adressaten, die Ortsbezüge 
          und die Begriffe mitliefern.
        </xsd:documentation>
      </xsd:annotation>
      <xsd:element name="ContainsModifiedBasicData" type="xsd:boolean" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Gibt an, ob Objekte des gegenständlichen Layers im Request veränderte Basisdaten enthalten. Betrifft alle 
            Daten, die nicht durch eines der anderen Attribute gesteuert werden, aus dem Layer (z.B. Subject) und aus 
            den MetaData (z.B. Annotation)
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContainsModifiedBinaryContent" type="xsd:boolean" minOccurs="0" maxOccurs="1" default="false">
        <xsd:annotation>
          <xsd:documentation>
            Gibt an, ob Objekte des gegenständlichen Layers im Request veränderte binäre Inhalte enthalten. Diese 
            Information ist nur für den Layer 0 relevant, bei allen anderen kann man diese Information in der Service-
            Implementierung ignorieren. Deshalb ist die Kardinalität des Elements - im Gegensatz zu allen anderen 
            Contains...-Elementen hier mit 0..1 und einem Standardwert "false" ausgeprägt. Der Sinn des Elements an 
            sich ist im Wesentlichen der, dass man so auch den Namen eines Dokuments im ELAK ändern kann, ohne gleich 
            erraten zu müssen, ob auch der Inhalt verändert wurde. Diese Konfigurationsmöglichkeit betrifft das 
            Documents-Element von SendDataInput, SendDataOutput und ReadDataOutput.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContainsModifiedCatchwords" type="xsd:boolean" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Gibt an, ob Objekte des gegenständlichen Layers im Request veränderte Begriffe enthalten. Betrifft die 
            MetaData-Catchwords.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContainsModifiedLocations" type="xsd:boolean" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Gibt an, ob Objekte des gegenständlichen Layers im Request veränderte Ortsbezüge enthalten. Betrifft die 
            MetaData-Locations.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContainsModifiedParticipants" type="xsd:boolean" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Gibt an, ob Objekte des gegenständlichen Layers im Request veränderte Adressaten enthalten. Betrifft die 
            MetaData-Participants.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContainsModifiedState" type="xsd:boolean" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Gibt an, ob Objekte des gegenständlichen Layers im Request veränderte Status enthalten. Betrifft MetaData-
            Closed, MetaData-Canceled und MetaData-Approved.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="LayerResponseControlType">
    <xsd:sequence minOccurs="1" maxOccurs="1">
      <xsd:annotation>
        <xsd:documentation>
          Mit dieser Struktur hat der Client die Möglichkeit, bei sämtlichen Operationen feingranular festzulegen, 
          welche Daten des gegenständlichen Layers der Server in die Response aufnehmen soll. Dafür wird die Menge 
          aller Daten eines Layers logisch in folgende Teilmengen zerlegt: die Basisdaten, die Begriffe, die 
          Ortsbezüge, die Adressaten, die Status, die binären Inhalte und die URL(s). Der Server kann theoretisch mehr 
          Daten als vom Client verlangt in die Response aufnehmen, aber er muss jedenfalls die verlangten Daten 
          inkludieren. Gerade im hochgradig verteilten System - und bei ELAK-TRANS haben wir ein solches - ist diese 
          Möglichkeit der expliziten Festlegung relevant, da mit diesem Mechanismus einerseits die Responses so klein 
          wie möglich gehalten und andererseits die Leseoperationen am Server auf ein Minimum reduziert werden können.
        </xsd:documentation>
      </xsd:annotation>
      <xsd:element name="IncludeBasicData" type="xsd:boolean" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Der Server soll die Basisdaten des Layers in die Response aufnehmen. Zu den Basisdaten gehören der Betreff,
            die Anmerkung, das Erstellungsdatum, der Objekttyp, der Bearbeiter, das Eingangsdatum und das Abfertigungs-
            bzw. Versanddatum; beim Layer 1 kommt der Geschäftsstück-Typ ("BusinessType") dazu. Ist dieses Element
            nicht angegeben, so hat das die gleiche Bedeutung als wäre der Wert "false". Betrifft alle Daten, die nicht 
            durch eines der anderen Attribute gesteuert werden, aus dem Layer (z.B. Subject) und aus den MetaData 
            (z.B. Annotation).
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludeBinaryContent" type="xsd:boolean" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Der Server soll die Inhalte, sofern es im Layer welche gibt, in die Response aufnehmen. Das heißt, für die
            Layer 1 bis 3 ist diese Information irrelevant, für Layer 0 essentiell. Ist dieses Element nicht angegeben,
            so hat das die gleiche Bedeutung als wäre der Wert "false". Betrifft das Documents-Element von 
            SendDataInput, SendDataOutput und ReadDataOutput.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludeCatchwords" type="xsd:boolean" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Der Server soll die Begriffe des gegenständlichen Layers in die Response aufnehmen. Ist dieses Element
            nicht angegeben, so hat das die gleiche Bedeutung als wäre der Wert "false". Betrifft die MetaData-
            Catchwords.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludeLocations" type="xsd:boolean" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Der Server soll die Ortsbezüge des gegenständlichen Layers in die Response aufnehmen. Ist dieses Element
            nicht angegeben, so hat das die gleiche Bedeutung als wäre der Wert "false". Betrifft die MetaData-
            Locations.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludeParticipants" type="xsd:boolean" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Der Server soll die Adressaten des gegenständlichen Layers in die Response aufnehmen. Ist dieses Element
            nicht angegeben, so hat das die gleiche Bedeutung als wäre der Wert "false". Betrifft die MetaData-
            Participants.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludeState" type="xsd:boolean" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Der Server soll die Statusinformationen des gegenständlichen Layers in die Response aufnehmen. Ist dieses 
            Element nicht angegeben, so hat das die gleiche Bedeutung als wäre der Wert "false".
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludeViewUrl" type="xsd:boolean" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Der Server soll die URL, z.B. eine solche zum Bearbeiten des Objekts im User-Interface, des
            gegenständlichen Layers in die Response aufnehmen. Ist dieses Element nicht angegeben, so hat das die
            gleiche Bedeutung als wäre der Wert "false". Betrifft die MetaData-Identifier-TechnicalID bzw. 
            MetaData-ReferencedIdentifier-TechnicalID.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludeUnspecifiedCoupledObjects" type="xsd:boolean" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Gibt an, dass auch solche in der ELAK-TRANS-Object-Mappingtabelle gemappten Objekte in der Response
            berücksichtigt werden sollen, die weder in der EDIDOC- noch in der LayerControl-Struktur explizit 
            erwähnt wurden. Wird das Element nicht angegeben, so ist dies mit dem Wert "false" gleichbedeutend. 
            Das spezifizierte Verhalten gilt für Objekte des gegenständlichen Layers, nicht für darunterliegende.
            Nur für Layer0, Layer1 und Layer2. Nicht für Layer3.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludeUncoupledObjects" type="xsd:boolean" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>
            Gibt an, ob auch solche Objekte im ELAK berücksichtigt werden sollen, die im ELAK erzeugt wurden und 
            nicht in der ELAK-TRANS-Object-Mappingtabelle gemappt sind. Wird das Element nicht angegeben, so ist 
            dies mit dem Wert "false" gleichbedeutend. Das spezifizierte Verhalten gilt für Objekte des 
            gegenständlichen Layers, nicht für darunterliegende. Nur für Layer0, Layer1 und Layer2. Nicht für Layer3.
          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

</xsd:schema>
