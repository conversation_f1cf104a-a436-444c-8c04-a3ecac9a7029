<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:edi="http://reference.e-government.gv.at/namespace/edidoc/20130808#" xmlns:p="http://reference.e-government.gv.at/namespace/persondata/20020228#" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:xpdl="http://www.wfmc.org/2009/XPDL2.2" targetNamespace="http://reference.e-government.gv.at/namespace/edidoc/20130808#" elementFormDefault="qualified">
	<!-- Importing PersonData-Schema -->
	<xs:import namespace="http://reference.e-government.gv.at/namespace/persondata/20020228#" schemaLocation="PersonData_20_en_dt.xsd"/>
	<!-- Importing XMLDSig-Schema -->
	<xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="W3C-XMLDSig.xsd"/>
	<!-- Importing XPDL-Schema -->
	<xs:import namespace="http://www.wfmc.org/2009/XPDL2.2" schemaLocation="bpmnxpdl_40a.xsd"/>
	<!-- EDIAKT Root-Element -->
	<xs:element name="Ediakt" type="edi:EdiaktType"/>
	<xs:complexType name="EdiaktType">
		<xs:sequence>
			<xs:element name="Header" type="edi:HeaderType"/>
			<xs:element name="ProcessData" type="edi:ProcessDataType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="MetaData" type="edi:MetaDataType"/>
			<xs:element name="Payload">
				<xs:complexType>
					<xs:choice maxOccurs="unbounded">
						<xs:element name="Layer3" type="edi:Layer3Type" maxOccurs="unbounded"/>
						<xs:element name="Layer2" type="edi:Layer2Type" maxOccurs="unbounded"/>
						<xs:element name="Layer1" type="edi:Layer1Type" maxOccurs="unbounded"/>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<xs:element ref="dsig:Signature" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HeaderType">
		<xs:sequence>
			<xs:element name="Receiver" type="edi:ReceiverType" minOccurs="0"/>
			<xs:element name="Sender" type="p:PersonDataType" minOccurs="0"/>
			<xs:element name="Purpose" type="edi:PurposeType" minOccurs="0"/>
			<xs:element name="CoveringLetter" type="xs:IDREF" minOccurs="0"/>
			<xs:element name="DeliveryInformation" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="Layer3" type="edi:Layer3Type"/>
	<xs:complexType name="Layer3Type">
		<xs:sequence>
			<xs:element name="Subject" type="xs:string"/>
			<xs:element name="Title" type="xs:token" minOccurs="0"/>
			<xs:element name="MetaData" type="edi:MetaDataType"/>
			<xs:element name="ProcessData" type="edi:ProcessDataType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Payload" minOccurs="0">
				<xs:complexType>
					<xs:choice maxOccurs="unbounded">
						<xs:element name="Layer2" type="edi:Layer2Type" maxOccurs="unbounded"/>
						<xs:element name="Layer1" type="edi:Layer1Type" maxOccurs="unbounded"/>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<xs:element name="References" type="edi:ReferencesType" minOccurs="0"/>
			<xs:element name="SpecialData" type="edi:SpecialDataType" minOccurs="0"/>
			<xs:element name="KeyValuePairs" type="edi:KeyValuePairsType" minOccurs="0"/>
			<xs:element name="ElakSignature" type="edi:ElakSignatureType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element ref="dsig:Signature" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="Layer2" type="edi:Layer2Type"/>
	<xs:complexType name="Layer2Type">
		<xs:sequence>
			<xs:element name="Subject" type="xs:string"/>
			<xs:element name="Title" type="xs:token" minOccurs="0"/>
			<xs:element name="MetaData" type="edi:MetaDataType"/>
			<xs:element name="ProcessData" type="edi:ProcessDataType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Payload" minOccurs="0">
				<xs:complexType>
					<xs:choice maxOccurs="unbounded">
						<xs:element name="Layer1" type="edi:Layer1Type" maxOccurs="unbounded"/>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<xs:element name="References" type="edi:ReferencesType" minOccurs="0"/>
			<xs:element name="SpecialData" type="edi:SpecialDataType" minOccurs="0"/>
			<xs:element name="KeyValuePairs" type="edi:KeyValuePairsType" minOccurs="0"/>
			<xs:element name="AccountingData" type="edi:AccountingDataType" minOccurs="0"/>
			<xs:element name="ElakSignature" type="edi:ElakSignatureType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element ref="dsig:Signature" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="Layer1" type="edi:Layer1Type"/>
	<xs:complexType name="Layer1Type">
		<xs:sequence>
			<xs:element name="Subject" type="xs:string"/>
			<xs:element name="Title" type="xs:token" minOccurs="0"/>
			<xs:element name="MetaData" type="edi:MetaDataType"/>
			<xs:element name="ProcessData" type="edi:ProcessDataType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="MainDocument" type="xs:IDREF" minOccurs="0"/>
			<xs:element name="Payload" minOccurs="0">
				<xs:complexType>
					<xs:choice maxOccurs="unbounded">
						<xs:element name="Layer0" type="edi:Layer0Type" maxOccurs="unbounded"/>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<xs:element name="References" type="edi:ReferencesType" minOccurs="0"/>
			<xs:element name="BusinessType" type="xs:token" minOccurs="0"/>
			<xs:element name="SpecialData" type="edi:SpecialDataType" minOccurs="0"/>
			<xs:element name="KeyValuePairs" type="edi:KeyValuePairsType" minOccurs="0"/>
			<xs:element name="AccountingData" type="edi:AccountingDataType" minOccurs="0"/>
			<xs:element name="ElakSignature" type="edi:ElakSignatureType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element ref="dsig:Signature" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Layer0Type">
		<xs:sequence>
			<xs:element name="Subject" type="xs:string" minOccurs="0"/>
			<xs:element name="Title" type="xs:token" minOccurs="0"/>
			<xs:element name="MetaData" type="edi:MetaDataType"/>
			<xs:element name="ProcessData" type="edi:ProcessDataType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Payload" type="edi:PayloadType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Representation" type="edi:PayloadType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="SpecialData" type="edi:SpecialDataType" minOccurs="0"/>
			<xs:element name="KeyValuePairs" type="edi:KeyValuePairsType" minOccurs="0"/>
			<xs:element name="ElakSignature" type="edi:ElakSignatureType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element ref="dsig:Signature" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:ID"/>
	</xs:complexType>
	<!-- Payload of the Documents -->
	<xs:element name="Payload" type="edi:PayloadType"/>
	<xs:complexType name="PayloadType">
		<xs:choice>
			<!-- Referenz auf ein binaeres Dokument im "documents"-Ordner des Edidoc-Archivs -->
			<xs:element name="BinaryDocument">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="EmbeddedFileURL" type="xs:token"/>
						<xs:element name="FileName" type="xs:token"/>
						<xs:element name="MIMEType" type="xs:token"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!-- Referenz auf ein Dokument, das via URL nachgeladen werden kann -->
			<xs:element name="DocumentReference">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="URL" type="xs:anyURI"/>
						<xs:element name="FileName" type="xs:token"/>
						<xs:element name="MIMEType" type="xs:token"/>
						<xs:element name="MD5Checksum" type="xs:string" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:choice>
		<xs:attribute name="id" type="xs:ID"/>
	</xs:complexType>
	<!-- Receiver of the Ediakt Package -->
	<xs:complexType name="ReceiverType">
		<xs:sequence>
			<xs:choice>
				<xs:sequence>
					<xs:element name="Authority" type="p:PersonDataType"/>
					<xs:element name="Addressee" type="p:PersonDataType" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:element name="Person" type="p:PersonDataType"/>
			</xs:choice>
			<!-- deprecated -->
			<xs:element name="ReferencedObjects" type="xs:IDREF" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Metadata -->
	<xs:complexType name="MetaDataType">
		<xs:sequence>
			<xs:element name="Identifier" type="edi:IdentificationType"/>
			<xs:element name="ParentSubject" type="xs:string" minOccurs="0"/>
			<xs:element name="ParentIdentifier" type="edi:IdentificationType" minOccurs="0"/>
			<xs:element name="Annotation" type="xs:string" minOccurs="0"/>
			<xs:element name="Date" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="InputAnnotation" type="xs:string" minOccurs="0"/>
			<xs:element name="Version" type="edi:VersionType" minOccurs="0"/>
			<xs:element name="Priority" type="xs:boolean" minOccurs="0"/>
			<xs:element name="DueDate" type="edi:DueDateType" minOccurs="0"/>
			<xs:element name="Closure" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Limited"/>
						<xs:enumeration value="Confidential"/>
						<xs:enumeration value="Secret"/>
						<xs:enumeration value="Top Secret"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ObjectType" type="xs:token" minOccurs="0"/>
			<xs:element name="Editor" type="p:PersonDataType" minOccurs="0"/>
			<xs:element name="OrganisationalUnit" type="xs:string" minOccurs="0"/>
			<xs:element name="Closed" type="xs:boolean" minOccurs="0"/>
			<xs:element name="Canceled" type="xs:boolean" minOccurs="0"/>
			<xs:element name="Approved" type="xs:boolean" minOccurs="0"/>
			<xs:element name="Suspended" type="xs:boolean" minOccurs="0"/>
			<xs:element name="Term" type="edi:TermType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="ReferencedIdentifier" type="edi:IdentificationType" minOccurs="0"/>
			<xs:element name="ReceivingDate" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="ActivityType" type="xs:string" minOccurs="0"/>
			<xs:element name="PhysicalObject" type="xs:string" minOccurs="0"/>
			<xs:element name="CassationPeriod" type="xs:int" minOccurs="0"/>
			<xs:element name="PlannedCassationDate" type="xs:date" minOccurs="0"/>
			<xs:element name="ArchiveDate" type="xs:date" minOccurs="0"/>
			<xs:element name="ArchiveAnnotation" type="xs:string" minOccurs="0"/>
			<xs:element name="Receiver" type="edi:ReceiverType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Sender" type="p:PersonDataType" minOccurs="0"/>
			<xs:element name="SendingDate" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="SendingAnnotation" type="xs:string" minOccurs="0"/>
			<xs:element name="SendingType" type="xs:token" minOccurs="0"/>
			<xs:element name="DeliveryDate" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="Keyword" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="CatchWord" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="LastChange" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="ExternalDate" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="PostmarkDate" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="Participants" type="edi:ParticipantsType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Locations" type="edi:LocationsType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="CreatedBy" type="p:PersonDataType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="LastChangeBy" type="p:PersonDataType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="SubmissionExcepted" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ApprovedBy" type="p:PersonDataType" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DueDateType">
		<xs:sequence>
			<xs:element name="Date" type="xs:dateTime"/>
			<xs:element name="Annotation" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TermType">
		<xs:sequence>
			<xs:element name="Date" type="xs:dateTime"/>
			<xs:element name="Annotation" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ParticipantsType">
		<xs:sequence>
			<xs:element name="Participant" type="p:PersonDataType"/>
			<xs:element name="TypeOfParticipation" type="xs:string"/>
			<xs:element name="SpecialData" type="edi:SpecialDataType" minOccurs="0"/>
			<xs:element name="KeyValuePairs" type="edi:KeyValuePairsType" minOccurs="0"/>
			<xs:element name="DispatchType" type="xs:string" minOccurs="0"/>
			<xs:element name="ReportingType" type="xs:string" minOccurs="0"/>
			<xs:element name="TranscriptRemark" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LocationsType">
		<xs:sequence>
			<xs:element name="Location" type="edi:LocationType"/>
			<xs:element name="TypeOfLocation" type="xs:string" minOccurs="0"/>
			<xs:element name="Description" type="xs:string" minOccurs="0"/>
			<xs:element name="SpecialData" type="edi:SpecialDataType" minOccurs="0"/>
			<xs:element name="KeyValuePairs" type="edi:KeyValuePairsType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
        <xs:complexType name="LocationType">
	    <xs:sequence>
	        <xs:choice minOccurs="1">
	    	    <xs:element name="CadastralRegisterAddress" type="edi:CadastralRegisterAddressType"/>
		    <xs:element name="GeoAddress" type="edi:GeoAddressType"/>
	        </xs:choice>
	    </xs:sequence>
        </xs:complexType>  	
	
	<!-- Idenitifcation Block -->
	<xs:element name="Identification" type="edi:IdentificationType"/>
	<xs:complexType name="IdentificationType">
		<xs:sequence>
			<xs:element name="Identification" type="xs:token"/>
			<xs:element name="ManagementIndicator" type="xs:token" minOccurs="0"/>
			<xs:element name="OrganisationalUnit" type="xs:token" minOccurs="0"/>
			<xs:element name="SubjectArea" type="xs:token" minOccurs="0"/>
			<xs:element name="PersonalRelation" type="p:PersonDataType" minOccurs="0"/>
			<xs:element name="OrdinalNumber" type="xs:token" minOccurs="0"/>
			<xs:element name="Year" type="xs:int" minOccurs="0"/>
			<xs:element name="ObjectID" type="xs:token" minOccurs="0"/>
			<xs:element name="SequentialNumber" type="xs:token" minOccurs="0"/>
			<xs:element name="TechnicalID" type="xs:token" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Version -->
	<xs:complexType name="VersionType">
		<xs:sequence>
			<xs:element name="VersionNumber" type="xs:token"/>
			<xs:element name="VersionDate" type="xs:dateTime"/>
			<xs:element name="ReferencedVersion" type="xs:IDREF" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Annotation" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Elak Signature (~ Unterschrift) -->
	<xs:complexType name="ElakSignatureType">
		<xs:sequence>
			<xs:element name="Issuer" type="p:PersonDataType"/>
			<xs:element name="Date" type="xs:dateTime"/>
			<xs:element name="Type" type="xs:token"/>
			<xs:element name="Annotation" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- References -->
	<xs:complexType name="ReferencesType">
		<xs:sequence>
			<!-- Miterledigt von -->
			<xs:element name="FinishedBy" type="edi:IdentificationType" minOccurs="0"/>
			<!-- ~Vorzahl -->
			<xs:element name="Predecessor" type="edi:IdentificationType" minOccurs="0" maxOccurs="unbounded"/>
			<!-- ~Nachzahl -->
			<xs:element name="Successor" type="edi:IdentificationType" minOccurs="0" maxOccurs="unbounded"/>
			<!-- ~Bezug -->
			<xs:element name="ReferTo" type="edi:IdentificationType" minOccurs="0" maxOccurs="unbounded"/>
			<!-- ~Miterledigt -->
			<xs:element name="AlsoFinished" type="edi:IdentificationType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PurposeType">
		<xs:sequence>
			<xs:element name="Type">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="for your information"/>
						<xs:enumeration value="for archive"/>
						<xs:enumeration value="make a statement"/>
						<xs:enumeration value="for handling"/>
						<xs:enumeration value="go on working"/>
						<xs:enumeration value="other"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Annotation" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="SpecialData" type="edi:SpecialDataType"/>
	<xs:complexType name="SpecialDataType">
		<xs:sequence>
			<xs:element name="XMLContent">
				<xs:complexType>
					<xs:sequence>
						<xs:any namespace="##any" processContents="lax"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="PreviewStylesheet" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:any namespace="##any" processContents="lax"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="KeyValuePairsType">
		<xs:sequence>
			<xs:element name="KeyValuePair" type="edi:KeyValuePairType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="KeyValuePairType" mixed="true">
		<xs:attribute name="Name" type="xs:NMTOKEN" use="required"/>
		<xs:attribute name="Value" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="AccountingDataType">
		<xs:sequence>
			<xs:element name="CostList" type="edi:CostListType" minOccurs="1" maxOccurs="unbounded"/>
			<xs:element name="GrossTotal" type="xs:double" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CostListType">
		<xs:sequence>
			<xs:element name="Amount" type="xs:double" minOccurs="0"/>
			<xs:element name="Quantity" type="xs:double" minOccurs="0"/>
			<xs:element name="Unit" type="xs:token" minOccurs="0"/>
			<xs:element name="TariffPost" type="xs:string" minOccurs="0"/>
			<xs:element name="CalculationPost" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ProcessDataType">
		<xs:sequence>
			<xs:element name="Model">
				<xs:complexType>
					<xs:sequence>
						<xs:element ref="xpdl:Package"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="RunTimeInfo" type="edi:RunTimeInfoType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RunTimeInfoType">
		<xs:sequence>
			<xs:element name="Entry" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ActivityRef" type="xs:NMTOKEN"/>
						<xs:element name="Person" type="p:PersonDataType" minOccurs="0"/>
						<xs:element name="StartTime" type="xs:dateTime" minOccurs="0"/>
						<xs:element name="EndTime" type="xs:dateTime" minOccurs="0"/>
						<xs:element name="DueDate" type="xs:date" minOccurs="0"/>
						<xs:element name="State" minOccurs="0">
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:enumeration value="Processing"/>
									<xs:enumeration value="Finished"/>
									<xs:enumeration value="Canceled"/>
									<xs:enumeration value="Unknown"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="Description" type="xs:string" minOccurs="0" maxOccurs="1"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
        <xs:element name="GeoAddress" type="edi:GeoAddressType">
             <xs:annotation>
                  <xs:documentation>GeoAdress including longitude, latitude and altitude</xs:documentation>
             </xs:annotation>
        </xs:element>
                 
        <xs:element name="CadastralRegisterAddress" type="edi:CadastralRegisterAddressType">
             <xs:annotation>
                 <xs:documentation>CadastralRegisterAdress including cadastral municipality, property number etc.</xs:documentation>
             </xs:annotation>
        </xs:element>
	<xs:complexType name="GeoAddressType">
		<xs:annotation>
			<xs:documentation>GeoAddress including longitude latitude altitude, e.g. 16.367724534737285 48.20766512964593 172.52</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="p:AbstractAddressType">
				<xs:sequence minOccurs="0">
					<xs:element name="GeoLocation" type="xs:token">
						<xs:annotation>
							<xs:documentation>longitude latitude altitude, e.g. 16.367724534737285 48.20766512964593 172.52</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="TypeOfGeoLocation" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>type/role of geo data, e.g. ist Bauwerber</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="TypeOfGeoCoordinates" type="xs:string">
						<xs:annotation>
							<xs:documentation>type of coordinate data, e.g. WSG...</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="CadastralRegisterAddressType">
		<xs:annotation>
			<xs:documentation>CadastralRegisterAdress including cadastral municipality, property number etc.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="p:AbstractAddressType">
				<xs:sequence minOccurs="0">
					<xs:element name="CadastralMunicipality" type="xs:string">
						<xs:annotation>
							<xs:documentation>designator of the cadastral municipality</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="PropertyNumber" type="xs:string">
						<xs:annotation>
							<xs:documentation>number of the property in the cadastral register</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
</xs:schema>
