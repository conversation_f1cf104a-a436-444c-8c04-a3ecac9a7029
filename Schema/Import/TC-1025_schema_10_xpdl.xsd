<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://www.wfmc.org/2002/XPDL1.0" xmlns:xpdl="http://www.wfmc.org/2002/XPDL1.0" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
   <xsd:element name="Activities">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Activity" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Activity">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Description" minOccurs="0"/>
            <xsd:element ref="xpdl:Limit" minOccurs="0"/>
            <xsd:choice>
               <xsd:element ref="xpdl:Route"/>
               <xsd:element ref="xpdl:Implementation"/>
               <xsd:element ref="xpdl:BlockActivity"/>
            </xsd:choice>
            <xsd:element ref="xpdl:Performer" minOccurs="0"/>
            <xsd:element ref="xpdl:StartMode" minOccurs="0"/>
            <xsd:element ref="xpdl:FinishMode" minOccurs="0"/>
            <xsd:element ref="xpdl:Priority" minOccurs="0"/>
            <xsd:element ref="xpdl:Deadline" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element ref="xpdl:SimulationInformation" minOccurs="0"/>
            <xsd:element ref="xpdl:Icon" minOccurs="0"/>
            <xsd:element ref="xpdl:Documentation" minOccurs="0"/>
            <xsd:element ref="xpdl:TransitionRestrictions" minOccurs="0"/>
            <xsd:element ref="xpdl:ExtendedAttributes" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="Name" type="xsd:string"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ActivitySet">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Activities" minOccurs="0"/>
            <xsd:element ref="xpdl:Transitions" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ActivitySets">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:ActivitySet" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ActualParameter" type="xsd:string"/>
   <xsd:element name="ActualParameters">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:ActualParameter" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Application">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Description" minOccurs="0"/>
            <xsd:choice>
               <xsd:element ref="xpdl:FormalParameters"/>
               <xsd:element ref="xpdl:ExternalReference" minOccurs="0"/>
            </xsd:choice>
            <xsd:element ref="xpdl:ExtendedAttributes" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="Name" type="xsd:string"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Applications">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Application" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ArrayType">
      <xsd:complexType>
         <xsd:group ref="xpdl:DataTypes"/>
         <xsd:attribute name="LowerIndex" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="UpperIndex" type="xsd:NMTOKEN" use="required"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Author" type="xsd:string"/>
   <xsd:element name="Automatic">
      <xsd:complexType/>
   </xsd:element>
   <xsd:element name="BasicType">
      <xsd:complexType>
         <xsd:attribute name="Type" use="required">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="STRING"/>
                  <xsd:enumeration value="FLOAT"/>
                  <xsd:enumeration value="INTEGER"/>
                  <xsd:enumeration value="REFERENCE"/>
                  <xsd:enumeration value="DATETIME"/>
                  <xsd:enumeration value="BOOLEAN"/>
                  <xsd:enumeration value="PERFORMER"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="BlockActivity">
      <xsd:complexType>
         <xsd:attribute name="BlockId" type="xsd:NMTOKEN" use="required"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Codepage" type="xsd:string"/>
   <xsd:element name="Condition">
      <xsd:complexType mixed="true">
         <xsd:choice minOccurs="0" maxOccurs="unbounded">
            <xsd:element ref="xpdl:Xpression"/>
         </xsd:choice>
         <xsd:attribute name="Type">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="CONDITION"/>
                  <xsd:enumeration value="OTHERWISE"/>
                  <xsd:enumeration value="EXCEPTION"/>
                  <xsd:enumeration value="DEFAULTEXCEPTION"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ConformanceClass">
      <xsd:complexType>
         <xsd:attribute name="GraphConformance">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="FULL_BLOCKED"/>
                  <xsd:enumeration value="LOOP_BLOCKED"/>
                  <xsd:enumeration value="NON_BLOCKED"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Cost" type="xsd:string"/>
   <xsd:element name="CostUnit" type="xsd:string"/>
   <xsd:element name="Countrykey" type="xsd:string"/>
   <xsd:element name="Created" type="xsd:string"/>
   <xsd:element name="DataField">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:DataType"/>
            <xsd:element ref="xpdl:InitialValue" minOccurs="0"/>
            <xsd:element ref="xpdl:Length" minOccurs="0"/>
            <xsd:element ref="xpdl:Description" minOccurs="0"/>
            <xsd:element ref="xpdl:ExtendedAttributes" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="Name" type="xsd:string"/>
         <xsd:attribute name="IsArray" default="FALSE">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="TRUE"/>
                  <xsd:enumeration value="FALSE"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="DataFields">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:DataField" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="DataType">
      <xsd:complexType>
         <xsd:group ref="xpdl:DataTypes"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:group name="DataTypes">
      <xsd:choice>
         <xsd:element ref="xpdl:BasicType"/>
         <xsd:element ref="xpdl:DeclaredType"/>
         <xsd:element ref="xpdl:SchemaType"/>
         <xsd:element ref="xpdl:ExternalReference"/>
         <xsd:element ref="xpdl:RecordType"/>
         <xsd:element ref="xpdl:UnionType"/>
         <xsd:element ref="xpdl:EnumerationType"/>
         <xsd:element ref="xpdl:ArrayType"/>
         <xsd:element ref="xpdl:ListType"/>
      </xsd:choice>
   </xsd:group>
   <xsd:element name="Deadline">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element name="DeadlineCondition" minOccurs="1" maxOccurs="1"/>
            <xsd:element name="ExceptionName" minOccurs="1" maxOccurs="1"/>
         </xsd:sequence>
         <xsd:attribute name="Execution">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="ASYNCHR"/>
                  <xsd:enumeration value="SYNCHR"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="DeclaredType">
      <xsd:complexType>
         <xsd:attribute name="Id" type="xsd:IDREF" use="required"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Description" type="xsd:string"/>
   <xsd:element name="Documentation" type="xsd:string"/>
   <xsd:element name="Duration" type="xsd:string"/>
   <xsd:element name="EnumerationType">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:EnumerationValue" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="EnumerationValue">
      <xsd:complexType>
         <xsd:attribute name="Name" type="xsd:NMTOKEN" use="required"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ExtendedAttribute">
      <xsd:complexType mixed="true">
         <xsd:choice minOccurs="0" maxOccurs="unbounded">
            <xsd:any processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:choice>
         <xsd:attribute name="Name" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="Value" type="xsd:string"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ExtendedAttributes">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:ExtendedAttribute" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ExternalPackage">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:ExtendedAttributes" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="href" type="xsd:string"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ExternalPackages">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:ExternalPackage" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ExternalReference">
      <xsd:complexType>
         <xsd:attribute name="xref" type="xsd:NMTOKEN" use="optional"/>
         <xsd:attribute name="location" type="xsd:anyURI" use="required"/>
         <xsd:attribute name="namespace" type="xsd:anyURI" use="optional"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="FinishMode">
      <xsd:complexType>
         <xsd:choice>
            <xsd:element ref="xpdl:Automatic"/>
            <xsd:element ref="xpdl:Manual"/>
         </xsd:choice>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="FormalParameter">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:DataType"/>
            <xsd:element ref="xpdl:Description" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="Index" type="xsd:NMTOKEN"/>
         <xsd:attribute name="Mode" default="IN">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="IN"/>
                  <xsd:enumeration value="OUT"/>
                  <xsd:enumeration value="INOUT"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="FormalParameters">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:FormalParameter" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Icon" type="xsd:string"/>
   <xsd:element name="Implementation">
      <xsd:complexType>
         <xsd:choice>
            <xsd:element ref="xpdl:No"/>
            <xsd:element ref="xpdl:Tool" maxOccurs="unbounded"/>
            <xsd:element ref="xpdl:SubFlow"/>
         </xsd:choice>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="InitialValue" type="xsd:string"/>
   <xsd:element name="Join">
      <xsd:complexType>
         <xsd:attribute name="Type">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="AND"/>
                  <xsd:enumeration value="XOR"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Length" type="xsd:string"/>
   <xsd:element name="Limit" type="xsd:string"/>
   <xsd:element name="ListType">
      <xsd:complexType>
         <xsd:group ref="xpdl:DataTypes"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Manual">
      <xsd:complexType/>
   </xsd:element>
   <xsd:element name="Member">
      <xsd:complexType>
         <xsd:group ref="xpdl:DataTypes"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="No">
      <xsd:complexType/>
   </xsd:element>
   <xsd:element name="Package">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:PackageHeader"/>
            <xsd:element ref="xpdl:RedefinableHeader" minOccurs="0"/>
            <xsd:element ref="xpdl:ConformanceClass" minOccurs="0"/>
            <xsd:element ref="xpdl:Script" minOccurs="0"/>
            <xsd:element ref="xpdl:ExternalPackages" minOccurs="0"/>
            <xsd:element ref="xpdl:TypeDeclarations" minOccurs="0"/>
            <xsd:element ref="xpdl:Participants" minOccurs="0"/>
            <xsd:element ref="xpdl:Applications" minOccurs="0"/>
            <xsd:element ref="xpdl:DataFields" minOccurs="0"/>
            <xsd:element ref="xpdl:WorkflowProcesses" minOccurs="0"/>
            <xsd:element ref="xpdl:ExtendedAttributes" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="Name" type="xsd:string"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="PackageHeader">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:XPDLVersion"/>
            <xsd:element ref="xpdl:Vendor"/>
            <xsd:element ref="xpdl:Created"/>
            <xsd:element ref="xpdl:Description" minOccurs="0"/>
            <xsd:element ref="xpdl:Documentation" minOccurs="0"/>
            <xsd:element ref="xpdl:PriorityUnit" minOccurs="0"/>
            <xsd:element ref="xpdl:CostUnit" minOccurs="0"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Participant">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:ParticipantType"/>
            <xsd:element ref="xpdl:Description" minOccurs="0"/>
            <xsd:element ref="xpdl:ExternalReference" minOccurs="0"/>
            <xsd:element ref="xpdl:ExtendedAttributes" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="Name" type="xsd:string"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ParticipantType">
      <xsd:complexType>
         <xsd:attribute name="Type" use="required">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="RESOURCE_SET"/>
                  <xsd:enumeration value="RESOURCE"/>
                  <xsd:enumeration value="ROLE"/>
                  <xsd:enumeration value="ORGANIZATIONAL_UNIT"/>
                  <xsd:enumeration value="HUMAN"/>
                  <xsd:enumeration value="SYSTEM"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Participants">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Participant" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Performer" type="xsd:string"/>
   <xsd:element name="Priority" type="xsd:string"/>
   <xsd:element name="PriorityUnit" type="xsd:string"/>
   <xsd:element name="ProcessHeader">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Created" minOccurs="0"/>
            <xsd:element ref="xpdl:Description" minOccurs="0"/>
            <xsd:element ref="xpdl:Priority" minOccurs="0"/>
            <xsd:element ref="xpdl:Limit" minOccurs="0"/>
            <xsd:element ref="xpdl:ValidFrom" minOccurs="0"/>
            <xsd:element ref="xpdl:ValidTo" minOccurs="0"/>
            <xsd:element ref="xpdl:TimeEstimation" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="DurationUnit">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="Y"/>
                  <xsd:enumeration value="M"/>
                  <xsd:enumeration value="D"/>
                  <xsd:enumeration value="h"/>
                  <xsd:enumeration value="m"/>
                  <xsd:enumeration value="s"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="RecordType">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Member" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="RedefinableHeader">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Author" minOccurs="0"/>
            <xsd:element ref="xpdl:Version" minOccurs="0"/>
            <xsd:element ref="xpdl:Codepage" minOccurs="0"/>
            <xsd:element ref="xpdl:Countrykey" minOccurs="0"/>
            <xsd:element ref="xpdl:Responsibles" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="PublicationStatus">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="UNDER_REVISION"/>
                  <xsd:enumeration value="RELEASED"/>
                  <xsd:enumeration value="UNDER_TEST"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Responsible" type="xsd:string"/>
   <xsd:element name="Responsibles">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Responsible" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Route">
      <xsd:complexType/>
   </xsd:element>
   <xsd:element name="SchemaType">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Script">
      <xsd:complexType>
         <xsd:attribute name="Type" type="xsd:string" use="required"/>
         <xsd:attribute name="Version" type="xsd:string" use="optional"/>
         <xsd:attribute name="Grammar" type="xsd:anyURI" use="optional"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="SimulationInformation">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Cost"/>
            <xsd:element ref="xpdl:TimeEstimation"/>
         </xsd:sequence>
         <xsd:attribute name="Instantiation">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="ONCE"/>
                  <xsd:enumeration value="MULTIPLE"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Split">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:TransitionRefs" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Type">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="AND"/>
                  <xsd:enumeration value="XOR"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="StartMode">
      <xsd:complexType>
         <xsd:choice>
            <xsd:element ref="xpdl:Automatic"/>
            <xsd:element ref="xpdl:Manual"/>
         </xsd:choice>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="SubFlow">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:ActualParameters" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:string" use="required"/>
         <xsd:attribute name="Execution">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="ASYNCHR"/>
                  <xsd:enumeration value="SYNCHR"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="TimeEstimation">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:WaitingTime" minOccurs="0"/>
            <xsd:element ref="xpdl:WorkingTime" minOccurs="0"/>
            <xsd:element ref="xpdl:Duration" minOccurs="0"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Tool">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:ActualParameters" minOccurs="0"/>
            <xsd:element ref="xpdl:Description" minOccurs="0"/>
            <xsd:element ref="xpdl:ExtendedAttributes" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="Type">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="APPLICATION"/>
                  <xsd:enumeration value="PROCEDURE"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Transition">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Condition" minOccurs="0"/>
            <xsd:element ref="xpdl:Description" minOccurs="0"/>
            <xsd:element ref="xpdl:ExtendedAttributes" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="From" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="To" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="Name" type="xsd:string"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="TransitionRef">
      <xsd:complexType>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="TransitionRefs">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:TransitionRef" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="TransitionRestriction">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Join" minOccurs="0"/>
            <xsd:element ref="xpdl:Split" minOccurs="0"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="TransitionRestrictions">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:TransitionRestriction" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="Transitions">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Transition" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="TypeDeclaration">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:group ref="xpdl:DataTypes"/>
            <xsd:element ref="xpdl:Description" minOccurs="0"/>
            <xsd:element ref="xpdl:ExtendedAttributes" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:ID" use="required"/>
         <xsd:attribute name="Name" type="xsd:string"/>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="TypeDeclarations">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:TypeDeclaration" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="UnionType">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:Member" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="ValidFrom" type="xsd:string"/>
   <xsd:element name="ValidTo" type="xsd:string"/>
   <xsd:element name="Vendor" type="xsd:string"/>
   <xsd:element name="Version" type="xsd:string"/>
   <xsd:element name="WaitingTime" type="xsd:string"/>
   <xsd:element name="WorkflowProcess">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:ProcessHeader"/>
            <xsd:element ref="xpdl:RedefinableHeader" minOccurs="0"/>
            <xsd:element ref="xpdl:FormalParameters" minOccurs="0"/>
            <xsd:element ref="xpdl:DataFields" minOccurs="0"/>
            <xsd:element ref="xpdl:Participants" minOccurs="0"/>
            <xsd:element ref="xpdl:Applications" minOccurs="0"/>
            <xsd:element ref="xpdl:ActivitySets" minOccurs="0"/>
            <xsd:element ref="xpdl:Activities" minOccurs="0"/>
            <xsd:element ref="xpdl:Transitions" minOccurs="0"/>
            <xsd:element ref="xpdl:ExtendedAttributes" minOccurs="0"/>
         </xsd:sequence>
         <xsd:attribute name="Id" type="xsd:NMTOKEN" use="required"/>
         <xsd:attribute name="Name" type="xsd:string"/>
         <xsd:attribute name="AccessLevel">
            <xsd:simpleType>
               <xsd:restriction base="xsd:NMTOKEN">
                  <xsd:enumeration value="PUBLIC"/>
                  <xsd:enumeration value="PRIVATE"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:attribute>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="WorkflowProcesses">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element ref="xpdl:WorkflowProcess" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
   <xsd:element name="WorkingTime" type="xsd:string"/>
   <xsd:element name="XPDLVersion" type="xsd:string"/>
   <xsd:element name="Xpression">
      <xsd:complexType mixed="true">
         <xsd:choice minOccurs="0" maxOccurs="unbounded">
            <xsd:any processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
         </xsd:choice>
      </xsd:complexType>
   </xsd:element>
</xsd:schema>